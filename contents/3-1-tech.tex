% !TEX root = ../nsfc-temp.tex
% \bibliography{../myexample.bib}

% \subsubsection{\texorpdfstring{\kaishu \CJKunderline[skip=false]{3.1 研究方案}}{}}

本项目采用的方案用到 Eisenstein 函数的解析性质、恒等式以及模性质，椭圆函数与准雅可比函数围道积分公式，以及仿射 Springer 纤维技术，均在申请人的前期工作中进行了深入的验证和发展，切实可行。此外，本项目将大量使用 Mathematica、SageMath 程序的符号运算和代数因式分解，偏微分方程组的级数求解，均是成熟的工具和思路。对空态结构的分析、寻找模变换矩阵的合适详细变换等需要多核大内存服务器的辅助。

下面分三点详细阐述本项目的具体研究方案。

\textbf{1. 分析模微分方程的准模性质}

基于申请人前期工作中的准模自举中用到的方法和思路，针对 Kac-Moody 代数或者其他较为简单的 $\mathbb{V}[\mathcal{T}]$，先写出手征代数的生成元的对易关系，通过直接计算 (包括利用 Mathematica 等电脑程序) 求得空态。结合含味的朱氏递推关系，得到含味模微分方程。此类计算原理比较直观，但需要做大量的符号计算，对于较大的手征代数例子，预期需要使用多核大内存工作站进行并行运算。

对于复杂的 $\mathbb{V}[\mathcal{T}]$ 代数，基于申请人发展的特殊函数围道积分公式计算舒尔指标/特征标的紧凑解析表达式。有拉格朗日量的 $\mathcal{N} = 2$ 超共形场论的舒尔指标、缺陷指标往往可以写成一些多变量围道积分，积分函数是 Eisenstein 级数、椭圆函数、多项式的乘积，
\begin{equation}
  \mathcal{I} \sim \oint \frac{da}{2\pi i a} a^m E_k \begin{bmatrix}
    \pm 1 \\ a z
  \end{bmatrix}E_k \begin{bmatrix}
    \pm 1 \\ a w
  \end{bmatrix}... f(a)
\end{equation}
这样的围道积分可以借助申请人所提出的积分公式或其推广来解析计算。由此得到的指标是由 Jacobi theta 函数 $\vartheta_i$、Eisenstein 级数的有限乘积和求和给出。

申请人的前期工作对这些特殊函数有深入的研究，发现了大量相关恒等式以及变换性质。一类有用的函数恒等式建立起 $\vartheta_i$ 的导数与 Eisenstein 级数的关系，以及 Eisenstein 级数的乘积恒等式，比如 $\partial_\mathfrak{b}\ln\vartheta_1(\mathfrak{b}) = 2\pi i E_1 \big[\substack{1 \\ b}\big]$。另一类有用的恒等式是 Eisenstein 级数的导数关系，比如 $b \partial_b E_1 \big[\substack{+1 \\ b}\big] + E_2(\tau) + E_1 \big[\substack{+1 \\ b}\big]^2 + 2 E_2 \big[\substack{+1 \\ b}\big] = 0$。利用这些恒等式，我们可以把对舒尔指标 $\mathcal{I}$ 的紧凑解析形式的求导转化为 Eisenstein 的多项式，并需要对此表达式进行因式分解，剥离舒尔指标本身，比如
\begin{equation}
  \sum_{\vec k} \lambda_{\vec k} E_{k_1} \Big[\substack{\pm 1 \\ b}\Big] E_{k_2} \Big[\substack{\pm 1 \\ b}\Big]
  = \mathcal{I} \cdot \sum_{\vec k} \mu_{\vec k} E_{k_1} \Big[\substack{\pm 1 \\ b}\Big] E_{k_2} \Big[\substack{\pm 1 \\ b}\Big] \ .
\end{equation}
因式分解要求我们充分利用 Eisenstein 级数的准雅可比性质：存在一组准雅可比形式的基底，使得所有的 Eisenstein 级数都可以用这组基底做展开。做基底展开后因式分解计算将变得相对简单，由此便得到含味模微分方程的形式。

前期工作对这些特殊函数的模性质有一系列的结果，可以直接用于研究含味模微分方程的模性质。模变换得到的非齐次表达式重新整理成一组较低阶的表达式的组合，验证它们是否同样湮灭所有已知的舒尔指标和缺陷指标。在 Kac-Moody 代数等简单手征代数中，尝试逆向构造这些低阶模微分方程所对应的空态，由此建立空态之间的隐藏模结构：
\begin{equation}
  \mathcal{E}_n \xrightarrow{S} \sum_{\vec \ell} \mathcal{E}_{\vec l} \qquad\Rightarrow \qquad |\mathcal{N}\rangle \xrightarrow{S} \sum_{\vec \ell} |\mathcal{N}_{\ell}\rangle \ .
\end{equation}
此计算原理直观但计算量大，为了提高计算效率，应使用 Mathematica 编写朱氏递推公式的高性能符号运算系统帮助分析。我们预期空态之间的模结构与手征代数的算符乘积展开具有一些深刻的联系，且具有一定普适性。




\textbf{2. 分析谱的模性质}


根据前期工作的结果，准模性质帮助我们从一个方程出发构造出一组方程，大大降低构造方程的难度。因此，申请人预期存在若干个特殊空态所对应的特殊含味模微分方程，从它们出发就可以得到所有关键的方程，该方程组足以唯一确定所有的含味特征标。

利用这些含味模微分方程，本项目通过级数求解的方式寻找方程组的所有独立的解。这些解应当对应表示特征标的某些线性组合。根据表示的寻常性、对数型性质，我们可以提取不同表示类型的数量。这些数量将用于与库伦分支物理的比对。

对方程进行级数求解并不能给出解的紧凑表达式。一旦验证了含味模微分方程具有准模性质，我们可以从已知紧凑解析表达式的舒尔指标、缺陷指标出发，通过各种模变换构造新的解，也是手征代数的表示特征标的某种线性组合。所有的模变换形成一个模变换群 $\Gamma$，而模变换所得到的所有表达式形成 $\Gamma$ 的轨道。这个轨道里面有无穷多个表达式，但申请人预期当中只有有限个是线性独立的。为此，申请人需要从中找出一组基底，计算这无穷多个表达式所张成的线性空间的维度。考察这个维度与寻常表示 (对数或非对数型) 的数量的关系，以及无味模微分方程的最小阶数 $n_\text{min}$ 的关系。



\textbf{3. 建立与库伦分支物理的联系}

库伦分支真空构型空间的几何结构有多种方式来获取。比较物理的方法是计算库伦分支指标 $\mathcal{I}_\text{C}$。指标 $\mathcal{I}_\text{C}$ 是 $L(k, 1) \times S^1$ 上的一个配分函数，是完整的棱镜空间指标 (lens space index) 的库伦分支极限。对于许多理论，包括有拉格朗日量的理论、Argyres-Douglas 理论等，$\mathcal{I}_\text{C}$ 都可以写成具体的有限重围道积分的表达式。对于有拉氏量的理论，规范自由度 (矢量多重态, vector multiplets) 贡献一些椭圆 $\Gamma$ 函数的乘积，物质自由度 (hypermultiplets) 则不做贡献，然后需要对积分函数做单位圆周上的围道积分。最后得到的结果是逸度 $\mathfrak{t}$ 的一个有理函数，并可以分解成
\begin{equation}
  \mathcal{I}_\text{C} = \sum_{i} \frac{\mathfrak{t}^{\mu_i k}}{\prod_{w_i}(1 - \mathfrak{t}^{w_i})} \ .
\end{equation}
其中，$\mu_i$ 是第 $i$ 个不动子流形上的 $U(1)_r$ 动量映射，$w_i$ 是 $U(1)_r$ 在不动子流形上作用的一些权 (weight)。通过这个分解，可以初步读出 Hitchin 模空间的不动子流形的数量、类型和基本几何数据。

借助前期工作中用到仿射 Springer 纤维技术，可以更加清晰地看出这些不动子流形隐藏的代数结构。对于 $A_1$ 类 clas-$\mathcal{S}$ 理论，对应的 Hitchin 模空间不动点已经有结果，比如对于亏格为 $g \ge 2$，无穿孔的情况，不动子流形分两类，一类是 $\Sigma_{g, 0}$ 上的稳定丛的模空间，另一类是 $\Sigma_{g, 0}$ 的对称积的某种覆盖空间。

把库伦分支构型空间的这些几何量与手征代数侧所得到的表示论数据进行比对，推广现有的结果到更多的理论上。比如，不动子流形应该与 $\mathbb{V}[\mathcal{T}]$ 的非对数型不可约表示一一对应，即与含味模微分方程的所有解一一对应；动量映射在各个不动子流形上的值减去一些常数 (称为 slope) 应该给出表示的共形权；在有对数解存在的情况下，模变换 $T$ 矩阵不可对角化，其 Jordan 块的大小应该给出不动子流形的维度信息。

通过分析手征代数的谱的模变换性质可以算出模变换矩阵。但是这个计算依赖基底的选取。因此，我们需要尝试采用不同的基底来计算 $S, T$ 等矩阵，并把结果与库伦分支指标比对。又由于库伦分支指标在 $\mathfrak{t}\to e^{2\pi i }$ 使有极点，这促使我们仿照现有文献去考虑一般 $\mathfrak{t}$ 的情况。这需要我们考虑 $S, T$ 矩阵的一个单参数形变 $S(\mathfrak{t}), T(\mathfrak{t})$，并保持 $S(\mathfrak{t})^4 = (T(\mathfrak{t})S(\mathfrak{t}))^3 = \mathbf{1}$。这些矩阵的探索需要用 Mathematica 和 numpy 等工具进行符号和数值的计算。