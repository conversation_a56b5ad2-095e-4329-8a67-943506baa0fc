<?xml version="1.0" encoding="UTF-8"?>
<bcf:controlfile version="3.8" bltxversion="3.17" xmlns:bcf="https://sourceforge.net/projects/biblatex">
  <!-- BIBER OPTIONS -->
  <bcf:options component="biber" type="global">
    <bcf:option type="singlevalued">
      <bcf:key>output_encoding</bcf:key>
      <bcf:value>utf8</bcf:value>
    </bcf:option>
    <bcf:option type="singlevalued">
      <bcf:key>input_encoding</bcf:key>
      <bcf:value>utf8</bcf:value>
    </bcf:option>
    <bcf:option type="singlevalued">
      <bcf:key>debug</bcf:key>
      <bcf:value>0</bcf:value>
    </bcf:option>
    <bcf:option type="singlevalued">
      <bcf:key>mincrossrefs</bcf:key>
      <bcf:value>2</bcf:value>
    </bcf:option>
    <bcf:option type="singlevalued">
      <bcf:key>minxrefs</bcf:key>
      <bcf:value>2</bcf:value>
    </bcf:option>
    <bcf:option type="singlevalued">
      <bcf:key>sortcase</bcf:key>
      <bcf:value>1</bcf:value>
    </bcf:option>
    <bcf:option type="singlevalued">
      <bcf:key>sortupper</bcf:key>
      <bcf:value>1</bcf:value>
    </bcf:option>
  </bcf:options>
  <!-- BIBLATEX OPTIONS -->
  <!-- GLOBAL -->
  <bcf:options component="biblatex" type="global">
    <bcf:option type="singlevalued">
      <bcf:key>alphaothers</bcf:key>
      <bcf:value>+</bcf:value>
    </bcf:option>
    <bcf:option type="singlevalued">
      <bcf:key>labelalpha</bcf:key>
      <bcf:value>0</bcf:value>
    </bcf:option>
    <bcf:option type="multivalued">
      <bcf:key>labelnamespec</bcf:key>
      <bcf:value order="1">shortauthor</bcf:value>
      <bcf:value order="2">author</bcf:value>
      <bcf:value order="3">shorteditor</bcf:value>
      <bcf:value order="4">editor</bcf:value>
      <bcf:value order="5">translator</bcf:value>
    </bcf:option>
    <bcf:option type="singlevalued">
      <bcf:key>labeltitle</bcf:key>
      <bcf:value>0</bcf:value>
    </bcf:option>
    <bcf:option type="multivalued">
      <bcf:key>labeltitlespec</bcf:key>
      <bcf:value order="1">shorttitle</bcf:value>
      <bcf:value order="2">title</bcf:value>
      <bcf:value order="3">maintitle</bcf:value>
    </bcf:option>
    <bcf:option type="singlevalued">
      <bcf:key>labeltitleyear</bcf:key>
      <bcf:value>0</bcf:value>
    </bcf:option>
    <bcf:option type="singlevalued">
      <bcf:key>labeldateparts</bcf:key>
      <bcf:value>0</bcf:value>
    </bcf:option>
    <bcf:option type="multivalued">
      <bcf:key>labeldatespec</bcf:key>
      <bcf:value order="1" type="field">date</bcf:value>
      <bcf:value order="2" type="field">year</bcf:value>
      <bcf:value order="3" type="field">eventdate</bcf:value>
      <bcf:value order="4" type="field">origdate</bcf:value>
      <bcf:value order="5" type="field">urldate</bcf:value>
      <bcf:value order="6" type="string">nodate</bcf:value>
    </bcf:option>
    <bcf:option type="singlevalued">
      <bcf:key>julian</bcf:key>
      <bcf:value>0</bcf:value>
    </bcf:option>
    <bcf:option type="singlevalued">
      <bcf:key>gregorianstart</bcf:key>
      <bcf:value>1582-10-15</bcf:value>
    </bcf:option>
    <bcf:option type="singlevalued">
      <bcf:key>maxalphanames</bcf:key>
      <bcf:value>3</bcf:value>
    </bcf:option>
    <bcf:option type="singlevalued">
      <bcf:key>maxbibnames</bcf:key>
      <bcf:value>999</bcf:value>
    </bcf:option>
    <bcf:option type="singlevalued">
      <bcf:key>maxcitenames</bcf:key>
      <bcf:value>2</bcf:value>
    </bcf:option>
    <bcf:option type="singlevalued">
      <bcf:key>maxsortnames</bcf:key>
      <bcf:value>999</bcf:value>
    </bcf:option>
    <bcf:option type="singlevalued">
      <bcf:key>maxitems</bcf:key>
      <bcf:value>3</bcf:value>
    </bcf:option>
    <bcf:option type="singlevalued">
      <bcf:key>minalphanames</bcf:key>
      <bcf:value>1</bcf:value>
    </bcf:option>
    <bcf:option type="singlevalued">
      <bcf:key>minbibnames</bcf:key>
      <bcf:value>1</bcf:value>
    </bcf:option>
    <bcf:option type="singlevalued">
      <bcf:key>mincitenames</bcf:key>
      <bcf:value>1</bcf:value>
    </bcf:option>
    <bcf:option type="singlevalued">
      <bcf:key>minsortnames</bcf:key>
      <bcf:value>1</bcf:value>
    </bcf:option>
    <bcf:option type="singlevalued">
      <bcf:key>minitems</bcf:key>
      <bcf:value>1</bcf:value>
    </bcf:option>
    <bcf:option type="singlevalued">
      <bcf:key>nohashothers</bcf:key>
      <bcf:value>0</bcf:value>
    </bcf:option>
    <bcf:option type="singlevalued">
      <bcf:key>noroman</bcf:key>
      <bcf:value>0</bcf:value>
    </bcf:option>
    <bcf:option type="singlevalued">
      <bcf:key>nosortothers</bcf:key>
      <bcf:value>0</bcf:value>
    </bcf:option>
    <bcf:option type="singlevalued">
      <bcf:key>singletitle</bcf:key>
      <bcf:value>0</bcf:value>
    </bcf:option>
    <bcf:option type="singlevalued">
      <bcf:key>skipbib</bcf:key>
      <bcf:value>0</bcf:value>
    </bcf:option>
    <bcf:option type="singlevalued">
      <bcf:key>skipbiblist</bcf:key>
      <bcf:value>0</bcf:value>
    </bcf:option>
    <bcf:option type="singlevalued">
      <bcf:key>skiplab</bcf:key>
      <bcf:value>0</bcf:value>
    </bcf:option>
    <bcf:option type="singlevalued">
      <bcf:key>sortalphaothers</bcf:key>
      <bcf:value>+</bcf:value>
    </bcf:option>
    <bcf:option type="singlevalued">
      <bcf:key>sortlocale</bcf:key>
      <bcf:value>english</bcf:value>
    </bcf:option>
    <bcf:option type="singlevalued">
      <bcf:key>sortingtemplatename</bcf:key>
      <bcf:value>none</bcf:value>
    </bcf:option>
    <bcf:option type="singlevalued">
      <bcf:key>sortsets</bcf:key>
      <bcf:value>0</bcf:value>
    </bcf:option>
    <bcf:option type="singlevalued">
      <bcf:key>uniquelist</bcf:key>
      <bcf:value>false</bcf:value>
    </bcf:option>
    <bcf:option type="singlevalued">
      <bcf:key>uniquename</bcf:key>
      <bcf:value>false</bcf:value>
    </bcf:option>
    <bcf:option type="singlevalued">
      <bcf:key>uniqueprimaryauthor</bcf:key>
      <bcf:value>0</bcf:value>
    </bcf:option>
    <bcf:option type="singlevalued">
      <bcf:key>uniquetitle</bcf:key>
      <bcf:value>0</bcf:value>
    </bcf:option>
    <bcf:option type="singlevalued">
      <bcf:key>uniquebaretitle</bcf:key>
      <bcf:value>0</bcf:value>
    </bcf:option>
    <bcf:option type="singlevalued">
      <bcf:key>uniquework</bcf:key>
      <bcf:value>0</bcf:value>
    </bcf:option>
    <bcf:option type="singlevalued">
      <bcf:key>useprefix</bcf:key>
      <bcf:value>0</bcf:value>
    </bcf:option>
    <bcf:option type="singlevalued">
      <bcf:key>useafterword</bcf:key>
      <bcf:value>1</bcf:value>
    </bcf:option>
    <bcf:option type="singlevalued">
      <bcf:key>useannotator</bcf:key>
      <bcf:value>1</bcf:value>
    </bcf:option>
    <bcf:option type="singlevalued">
      <bcf:key>useauthor</bcf:key>
      <bcf:value>1</bcf:value>
    </bcf:option>
    <bcf:option type="singlevalued">
      <bcf:key>usebookauthor</bcf:key>
      <bcf:value>1</bcf:value>
    </bcf:option>
    <bcf:option type="singlevalued">
      <bcf:key>usecommentator</bcf:key>
      <bcf:value>1</bcf:value>
    </bcf:option>
    <bcf:option type="singlevalued">
      <bcf:key>useeditor</bcf:key>
      <bcf:value>1</bcf:value>
    </bcf:option>
    <bcf:option type="singlevalued">
      <bcf:key>useeditora</bcf:key>
      <bcf:value>1</bcf:value>
    </bcf:option>
    <bcf:option type="singlevalued">
      <bcf:key>useeditorb</bcf:key>
      <bcf:value>1</bcf:value>
    </bcf:option>
    <bcf:option type="singlevalued">
      <bcf:key>useeditorc</bcf:key>
      <bcf:value>1</bcf:value>
    </bcf:option>
    <bcf:option type="singlevalued">
      <bcf:key>useforeword</bcf:key>
      <bcf:value>1</bcf:value>
    </bcf:option>
    <bcf:option type="singlevalued">
      <bcf:key>useholder</bcf:key>
      <bcf:value>1</bcf:value>
    </bcf:option>
    <bcf:option type="singlevalued">
      <bcf:key>useintroduction</bcf:key>
      <bcf:value>1</bcf:value>
    </bcf:option>
    <bcf:option type="singlevalued">
      <bcf:key>usenamea</bcf:key>
      <bcf:value>1</bcf:value>
    </bcf:option>
    <bcf:option type="singlevalued">
      <bcf:key>usenameb</bcf:key>
      <bcf:value>1</bcf:value>
    </bcf:option>
    <bcf:option type="singlevalued">
      <bcf:key>usenamec</bcf:key>
      <bcf:value>1</bcf:value>
    </bcf:option>
    <bcf:option type="singlevalued">
      <bcf:key>usetranslator</bcf:key>
      <bcf:value>0</bcf:value>
    </bcf:option>
    <bcf:option type="singlevalued">
      <bcf:key>useshortauthor</bcf:key>
      <bcf:value>1</bcf:value>
    </bcf:option>
    <bcf:option type="singlevalued">
      <bcf:key>useshorteditor</bcf:key>
      <bcf:value>1</bcf:value>
    </bcf:option>
  </bcf:options>
  <!-- online -->
  <bcf:options component="biblatex" type="online">
    <bcf:option type="singlevalued">
      <bcf:key>labelalpha</bcf:key>
      <bcf:value>0</bcf:value>
    </bcf:option>
    <bcf:option type="multivalued">
      <bcf:key>labelnamespec</bcf:key>
      <bcf:value order="1">shortauthor</bcf:value>
      <bcf:value order="2">author</bcf:value>
      <bcf:value order="3">shorteditor</bcf:value>
      <bcf:value order="4">editor</bcf:value>
      <bcf:value order="5">translator</bcf:value>
    </bcf:option>
    <bcf:option type="singlevalued">
      <bcf:key>labeltitle</bcf:key>
      <bcf:value>0</bcf:value>
    </bcf:option>
    <bcf:option type="multivalued">
      <bcf:key>labeltitlespec</bcf:key>
      <bcf:value order="1">shorttitle</bcf:value>
      <bcf:value order="2">title</bcf:value>
      <bcf:value order="3">maintitle</bcf:value>
    </bcf:option>
    <bcf:option type="singlevalued">
      <bcf:key>labeltitleyear</bcf:key>
      <bcf:value>0</bcf:value>
    </bcf:option>
    <bcf:option type="singlevalued">
      <bcf:key>labeldateparts</bcf:key>
      <bcf:value>0</bcf:value>
    </bcf:option>
    <bcf:option type="multivalued">
      <bcf:key>labeldatespec</bcf:key>
      <bcf:value order="1" type="field">date</bcf:value>
      <bcf:value order="2" type="field">year</bcf:value>
      <bcf:value order="3" type="field">eventdate</bcf:value>
      <bcf:value order="4" type="field">origdate</bcf:value>
      <bcf:value order="5" type="field">urldate</bcf:value>
      <bcf:value order="6" type="string">nodate</bcf:value>
    </bcf:option>
    <bcf:option type="singlevalued">
      <bcf:key>maxalphanames</bcf:key>
      <bcf:value>3</bcf:value>
    </bcf:option>
    <bcf:option type="singlevalued">
      <bcf:key>maxbibnames</bcf:key>
      <bcf:value>999</bcf:value>
    </bcf:option>
    <bcf:option type="singlevalued">
      <bcf:key>maxcitenames</bcf:key>
      <bcf:value>2</bcf:value>
    </bcf:option>
    <bcf:option type="singlevalued">
      <bcf:key>maxsortnames</bcf:key>
      <bcf:value>999</bcf:value>
    </bcf:option>
    <bcf:option type="singlevalued">
      <bcf:key>maxitems</bcf:key>
      <bcf:value>3</bcf:value>
    </bcf:option>
    <bcf:option type="singlevalued">
      <bcf:key>minalphanames</bcf:key>
      <bcf:value>1</bcf:value>
    </bcf:option>
    <bcf:option type="singlevalued">
      <bcf:key>minbibnames</bcf:key>
      <bcf:value>1</bcf:value>
    </bcf:option>
    <bcf:option type="singlevalued">
      <bcf:key>mincitenames</bcf:key>
      <bcf:value>1</bcf:value>
    </bcf:option>
    <bcf:option type="singlevalued">
      <bcf:key>minsortnames</bcf:key>
      <bcf:value>1</bcf:value>
    </bcf:option>
    <bcf:option type="singlevalued">
      <bcf:key>minitems</bcf:key>
      <bcf:value>1</bcf:value>
    </bcf:option>
    <bcf:option type="singlevalued">
      <bcf:key>nohashothers</bcf:key>
      <bcf:value>0</bcf:value>
    </bcf:option>
    <bcf:option type="singlevalued">
      <bcf:key>noroman</bcf:key>
      <bcf:value>0</bcf:value>
    </bcf:option>
    <bcf:option type="singlevalued">
      <bcf:key>nosortothers</bcf:key>
      <bcf:value>0</bcf:value>
    </bcf:option>
    <bcf:option type="singlevalued">
      <bcf:key>singletitle</bcf:key>
      <bcf:value>0</bcf:value>
    </bcf:option>
    <bcf:option type="singlevalued">
      <bcf:key>skipbib</bcf:key>
      <bcf:value>0</bcf:value>
    </bcf:option>
    <bcf:option type="singlevalued">
      <bcf:key>skiplab</bcf:key>
      <bcf:value>0</bcf:value>
    </bcf:option>
    <bcf:option type="singlevalued">
      <bcf:key>skipbiblist</bcf:key>
      <bcf:value>0</bcf:value>
    </bcf:option>
    <bcf:option type="singlevalued">
      <bcf:key>uniquelist</bcf:key>
      <bcf:value>false</bcf:value>
    </bcf:option>
    <bcf:option type="singlevalued">
      <bcf:key>uniquename</bcf:key>
      <bcf:value>false</bcf:value>
    </bcf:option>
    <bcf:option type="singlevalued">
      <bcf:key>uniqueprimaryauthor</bcf:key>
      <bcf:value>0</bcf:value>
    </bcf:option>
    <bcf:option type="singlevalued">
      <bcf:key>uniquetitle</bcf:key>
      <bcf:value>0</bcf:value>
    </bcf:option>
    <bcf:option type="singlevalued">
      <bcf:key>uniquebaretitle</bcf:key>
      <bcf:value>0</bcf:value>
    </bcf:option>
    <bcf:option type="singlevalued">
      <bcf:key>uniquework</bcf:key>
      <bcf:value>0</bcf:value>
    </bcf:option>
    <bcf:option type="singlevalued">
      <bcf:key>useprefix</bcf:key>
      <bcf:value>0</bcf:value>
    </bcf:option>
    <bcf:option type="singlevalued">
      <bcf:key>useafterword</bcf:key>
      <bcf:value>1</bcf:value>
    </bcf:option>
    <bcf:option type="singlevalued">
      <bcf:key>useannotator</bcf:key>
      <bcf:value>1</bcf:value>
    </bcf:option>
    <bcf:option type="singlevalued">
      <bcf:key>useauthor</bcf:key>
      <bcf:value>1</bcf:value>
    </bcf:option>
    <bcf:option type="singlevalued">
      <bcf:key>usebookauthor</bcf:key>
      <bcf:value>1</bcf:value>
    </bcf:option>
    <bcf:option type="singlevalued">
      <bcf:key>usecommentator</bcf:key>
      <bcf:value>1</bcf:value>
    </bcf:option>
    <bcf:option type="singlevalued">
      <bcf:key>useeditor</bcf:key>
      <bcf:value>1</bcf:value>
    </bcf:option>
    <bcf:option type="singlevalued">
      <bcf:key>useeditora</bcf:key>
      <bcf:value>1</bcf:value>
    </bcf:option>
    <bcf:option type="singlevalued">
      <bcf:key>useeditorb</bcf:key>
      <bcf:value>1</bcf:value>
    </bcf:option>
    <bcf:option type="singlevalued">
      <bcf:key>useeditorc</bcf:key>
      <bcf:value>1</bcf:value>
    </bcf:option>
    <bcf:option type="singlevalued">
      <bcf:key>useforeword</bcf:key>
      <bcf:value>1</bcf:value>
    </bcf:option>
    <bcf:option type="singlevalued">
      <bcf:key>useholder</bcf:key>
      <bcf:value>1</bcf:value>
    </bcf:option>
    <bcf:option type="singlevalued">
      <bcf:key>useintroduction</bcf:key>
      <bcf:value>1</bcf:value>
    </bcf:option>
    <bcf:option type="singlevalued">
      <bcf:key>usenamea</bcf:key>
      <bcf:value>1</bcf:value>
    </bcf:option>
    <bcf:option type="singlevalued">
      <bcf:key>usenameb</bcf:key>
      <bcf:value>1</bcf:value>
    </bcf:option>
    <bcf:option type="singlevalued">
      <bcf:key>usenamec</bcf:key>
      <bcf:value>1</bcf:value>
    </bcf:option>
    <bcf:option type="singlevalued">
      <bcf:key>usetranslator</bcf:key>
      <bcf:value>0</bcf:value>
    </bcf:option>
    <bcf:option type="singlevalued">
      <bcf:key>useshortauthor</bcf:key>
      <bcf:value>1</bcf:value>
    </bcf:option>
    <bcf:option type="singlevalued">
      <bcf:key>useshorteditor</bcf:key>
      <bcf:value>1</bcf:value>
    </bcf:option>
  </bcf:options>
  <!-- BIBLATEX OPTION SCOPE -->
  <bcf:optionscope type="GLOBAL">
    <bcf:option datatype="xml">datamodel</bcf:option>
    <bcf:option datatype="xml">labelalphanametemplate</bcf:option>
    <bcf:option datatype="xml">labelalphatemplate</bcf:option>
    <bcf:option datatype="xml">inheritance</bcf:option>
    <bcf:option datatype="xml">translit</bcf:option>
    <bcf:option datatype="xml">uniquenametemplate</bcf:option>
    <bcf:option datatype="xml">sortingnamekeytemplate</bcf:option>
    <bcf:option datatype="xml">sortingtemplate</bcf:option>
    <bcf:option datatype="xml">extradatespec</bcf:option>
    <bcf:option datatype="xml">labelnamespec</bcf:option>
    <bcf:option datatype="xml">labeltitlespec</bcf:option>
    <bcf:option datatype="xml">labeldatespec</bcf:option>
    <bcf:option datatype="string">controlversion</bcf:option>
    <bcf:option datatype="string">alphaothers</bcf:option>
    <bcf:option datatype="string">sortalphaothers</bcf:option>
    <bcf:option datatype="string">presort</bcf:option>
    <bcf:option datatype="string">texencoding</bcf:option>
    <bcf:option datatype="string">bibencoding</bcf:option>
    <bcf:option datatype="string">sortingtemplatename</bcf:option>
    <bcf:option datatype="string">sortlocale</bcf:option>
    <bcf:option datatype="string">language</bcf:option>
    <bcf:option datatype="string">autolang</bcf:option>
    <bcf:option datatype="string">langhook</bcf:option>
    <bcf:option datatype="string">indexing</bcf:option>
    <bcf:option datatype="string">hyperref</bcf:option>
    <bcf:option datatype="string">backrefsetstyle</bcf:option>
    <bcf:option datatype="string">block</bcf:option>
    <bcf:option datatype="string">pagetracker</bcf:option>
    <bcf:option datatype="string">citecounter</bcf:option>
    <bcf:option datatype="string">citetracker</bcf:option>
    <bcf:option datatype="string">ibidtracker</bcf:option>
    <bcf:option datatype="string">idemtracker</bcf:option>
    <bcf:option datatype="string">opcittracker</bcf:option>
    <bcf:option datatype="string">loccittracker</bcf:option>
    <bcf:option datatype="string">labeldate</bcf:option>
    <bcf:option datatype="string">labeltime</bcf:option>
    <bcf:option datatype="string">dateera</bcf:option>
    <bcf:option datatype="string">date</bcf:option>
    <bcf:option datatype="string">time</bcf:option>
    <bcf:option datatype="string">eventdate</bcf:option>
    <bcf:option datatype="string">eventtime</bcf:option>
    <bcf:option datatype="string">origdate</bcf:option>
    <bcf:option datatype="string">origtime</bcf:option>
    <bcf:option datatype="string">urldate</bcf:option>
    <bcf:option datatype="string">urltime</bcf:option>
    <bcf:option datatype="string">alldatesusetime</bcf:option>
    <bcf:option datatype="string">alldates</bcf:option>
    <bcf:option datatype="string">alltimes</bcf:option>
    <bcf:option datatype="string">gregorianstart</bcf:option>
    <bcf:option datatype="string">autocite</bcf:option>
    <bcf:option datatype="string">notetype</bcf:option>
    <bcf:option datatype="string">uniquelist</bcf:option>
    <bcf:option datatype="string">uniquename</bcf:option>
    <bcf:option datatype="string">refsection</bcf:option>
    <bcf:option datatype="string">refsegment</bcf:option>
    <bcf:option datatype="string">citereset</bcf:option>
    <bcf:option datatype="string">sortlos</bcf:option>
    <bcf:option datatype="string">babel</bcf:option>
    <bcf:option datatype="string">datelabel</bcf:option>
    <bcf:option datatype="string">backrefstyle</bcf:option>
    <bcf:option datatype="string">arxiv</bcf:option>
    <bcf:option datatype="boolean">familyinits</bcf:option>
    <bcf:option datatype="boolean">giveninits</bcf:option>
    <bcf:option datatype="boolean">prefixinits</bcf:option>
    <bcf:option datatype="boolean">suffixinits</bcf:option>
    <bcf:option datatype="boolean">useafterword</bcf:option>
    <bcf:option datatype="boolean">useannotator</bcf:option>
    <bcf:option datatype="boolean">useauthor</bcf:option>
    <bcf:option datatype="boolean">usebookauthor</bcf:option>
    <bcf:option datatype="boolean">usecommentator</bcf:option>
    <bcf:option datatype="boolean">useeditor</bcf:option>
    <bcf:option datatype="boolean">useeditora</bcf:option>
    <bcf:option datatype="boolean">useeditorb</bcf:option>
    <bcf:option datatype="boolean">useeditorc</bcf:option>
    <bcf:option datatype="boolean">useforeword</bcf:option>
    <bcf:option datatype="boolean">useholder</bcf:option>
    <bcf:option datatype="boolean">useintroduction</bcf:option>
    <bcf:option datatype="boolean">usenamea</bcf:option>
    <bcf:option datatype="boolean">usenameb</bcf:option>
    <bcf:option datatype="boolean">usenamec</bcf:option>
    <bcf:option datatype="boolean">usetranslator</bcf:option>
    <bcf:option datatype="boolean">useshortauthor</bcf:option>
    <bcf:option datatype="boolean">useshorteditor</bcf:option>
    <bcf:option datatype="boolean">debug</bcf:option>
    <bcf:option datatype="boolean">loadfiles</bcf:option>
    <bcf:option datatype="boolean">safeinputenc</bcf:option>
    <bcf:option datatype="boolean">sortcase</bcf:option>
    <bcf:option datatype="boolean">sortupper</bcf:option>
    <bcf:option datatype="boolean">terseinits</bcf:option>
    <bcf:option datatype="boolean">abbreviate</bcf:option>
    <bcf:option datatype="boolean">dateabbrev</bcf:option>
    <bcf:option datatype="boolean">clearlang</bcf:option>
    <bcf:option datatype="boolean">sortcites</bcf:option>
    <bcf:option datatype="boolean">sortsets</bcf:option>
    <bcf:option datatype="boolean">backref</bcf:option>
    <bcf:option datatype="boolean">backreffloats</bcf:option>
    <bcf:option datatype="boolean">trackfloats</bcf:option>
    <bcf:option datatype="boolean">parentracker</bcf:option>
    <bcf:option datatype="boolean">labeldateusetime</bcf:option>
    <bcf:option datatype="boolean">datecirca</bcf:option>
    <bcf:option datatype="boolean">dateuncertain</bcf:option>
    <bcf:option datatype="boolean">dateusetime</bcf:option>
    <bcf:option datatype="boolean">eventdateusetime</bcf:option>
    <bcf:option datatype="boolean">origdateusetime</bcf:option>
    <bcf:option datatype="boolean">urldateusetime</bcf:option>
    <bcf:option datatype="boolean">julian</bcf:option>
    <bcf:option datatype="boolean">datezeros</bcf:option>
    <bcf:option datatype="boolean">timezeros</bcf:option>
    <bcf:option datatype="boolean">timezones</bcf:option>
    <bcf:option datatype="boolean">seconds</bcf:option>
    <bcf:option datatype="boolean">autopunct</bcf:option>
    <bcf:option datatype="boolean">punctfont</bcf:option>
    <bcf:option datatype="boolean">labelnumber</bcf:option>
    <bcf:option datatype="boolean">labelalpha</bcf:option>
    <bcf:option datatype="boolean">labeltitle</bcf:option>
    <bcf:option datatype="boolean">labeltitleyear</bcf:option>
    <bcf:option datatype="boolean">labeldateparts</bcf:option>
    <bcf:option datatype="boolean">nohashothers</bcf:option>
    <bcf:option datatype="boolean">nosortothers</bcf:option>
    <bcf:option datatype="boolean">noroman</bcf:option>
    <bcf:option datatype="boolean">singletitle</bcf:option>
    <bcf:option datatype="boolean">uniquetitle</bcf:option>
    <bcf:option datatype="boolean">uniquebaretitle</bcf:option>
    <bcf:option datatype="boolean">uniquework</bcf:option>
    <bcf:option datatype="boolean">uniqueprimaryauthor</bcf:option>
    <bcf:option datatype="boolean">defernumbers</bcf:option>
    <bcf:option datatype="boolean">locallabelwidth</bcf:option>
    <bcf:option datatype="boolean">bibwarn</bcf:option>
    <bcf:option datatype="boolean">useprefix</bcf:option>
    <bcf:option datatype="boolean">skipbib</bcf:option>
    <bcf:option datatype="boolean">skipbiblist</bcf:option>
    <bcf:option datatype="boolean">skiplab</bcf:option>
    <bcf:option datatype="boolean">dataonly</bcf:option>
    <bcf:option datatype="boolean">defernums</bcf:option>
    <bcf:option datatype="boolean">firstinits</bcf:option>
    <bcf:option datatype="boolean">sortfirstinits</bcf:option>
    <bcf:option datatype="boolean">sortgiveninits</bcf:option>
    <bcf:option datatype="boolean">labelyear</bcf:option>
    <bcf:option datatype="boolean">isbn</bcf:option>
    <bcf:option datatype="boolean">url</bcf:option>
    <bcf:option datatype="boolean">doi</bcf:option>
    <bcf:option datatype="boolean">eprint</bcf:option>
    <bcf:option datatype="boolean">related</bcf:option>
    <bcf:option datatype="boolean">subentry</bcf:option>
    <bcf:option datatype="boolean">articletitle</bcf:option>
    <bcf:option datatype="boolean">chaptertitle</bcf:option>
    <bcf:option datatype="boolean">pageranges</bcf:option>
    <bcf:option datatype="boolean">biblabel</bcf:option>
    <bcf:option datatype="boolean">subentrycomp</bcf:option>
    <bcf:option datatype="boolean">bibtexcaseprotection</bcf:option>
    <bcf:option datatype="integer">mincrossrefs</bcf:option>
    <bcf:option datatype="integer">minxrefs</bcf:option>
    <bcf:option datatype="integer">maxnames</bcf:option>
    <bcf:option datatype="integer">minnames</bcf:option>
    <bcf:option datatype="integer">maxbibnames</bcf:option>
    <bcf:option datatype="integer">minbibnames</bcf:option>
    <bcf:option datatype="integer">maxcitenames</bcf:option>
    <bcf:option datatype="integer">mincitenames</bcf:option>
    <bcf:option datatype="integer">maxsortnames</bcf:option>
    <bcf:option datatype="integer">minsortnames</bcf:option>
    <bcf:option datatype="integer">maxitems</bcf:option>
    <bcf:option datatype="integer">minitems</bcf:option>
    <bcf:option datatype="integer">maxalphanames</bcf:option>
    <bcf:option datatype="integer">minalphanames</bcf:option>
    <bcf:option datatype="integer">maxparens</bcf:option>
    <bcf:option datatype="integer">dateeraauto</bcf:option>
  </bcf:optionscope>
  <bcf:optionscope type="ENTRYTYPE">
    <bcf:option datatype="string">alphaothers</bcf:option>
    <bcf:option datatype="string">sortalphaothers</bcf:option>
    <bcf:option datatype="string">presort</bcf:option>
    <bcf:option datatype="string">indexing</bcf:option>
    <bcf:option datatype="string">citetracker</bcf:option>
    <bcf:option datatype="string">ibidtracker</bcf:option>
    <bcf:option datatype="string">idemtracker</bcf:option>
    <bcf:option datatype="string">opcittracker</bcf:option>
    <bcf:option datatype="string">loccittracker</bcf:option>
    <bcf:option datatype="string">uniquelist</bcf:option>
    <bcf:option datatype="string">uniquename</bcf:option>
    <bcf:option datatype="boolean">familyinits</bcf:option>
    <bcf:option datatype="boolean">giveninits</bcf:option>
    <bcf:option datatype="boolean">prefixinits</bcf:option>
    <bcf:option datatype="boolean">suffixinits</bcf:option>
    <bcf:option datatype="boolean">useafterword</bcf:option>
    <bcf:option datatype="boolean">useannotator</bcf:option>
    <bcf:option datatype="boolean">useauthor</bcf:option>
    <bcf:option datatype="boolean">usebookauthor</bcf:option>
    <bcf:option datatype="boolean">usecommentator</bcf:option>
    <bcf:option datatype="boolean">useeditor</bcf:option>
    <bcf:option datatype="boolean">useeditora</bcf:option>
    <bcf:option datatype="boolean">useeditorb</bcf:option>
    <bcf:option datatype="boolean">useeditorc</bcf:option>
    <bcf:option datatype="boolean">useforeword</bcf:option>
    <bcf:option datatype="boolean">useholder</bcf:option>
    <bcf:option datatype="boolean">useintroduction</bcf:option>
    <bcf:option datatype="boolean">usenamea</bcf:option>
    <bcf:option datatype="boolean">usenameb</bcf:option>
    <bcf:option datatype="boolean">usenamec</bcf:option>
    <bcf:option datatype="boolean">usetranslator</bcf:option>
    <bcf:option datatype="boolean">useshortauthor</bcf:option>
    <bcf:option datatype="boolean">useshorteditor</bcf:option>
    <bcf:option datatype="boolean">terseinits</bcf:option>
    <bcf:option datatype="boolean">abbreviate</bcf:option>
    <bcf:option datatype="boolean">dateabbrev</bcf:option>
    <bcf:option datatype="boolean">clearlang</bcf:option>
    <bcf:option datatype="boolean">labelnumber</bcf:option>
    <bcf:option datatype="boolean">labelalpha</bcf:option>
    <bcf:option datatype="boolean">labeltitle</bcf:option>
    <bcf:option datatype="boolean">labeltitleyear</bcf:option>
    <bcf:option datatype="boolean">labeldateparts</bcf:option>
    <bcf:option datatype="boolean">nohashothers</bcf:option>
    <bcf:option datatype="boolean">nosortothers</bcf:option>
    <bcf:option datatype="boolean">noroman</bcf:option>
    <bcf:option datatype="boolean">singletitle</bcf:option>
    <bcf:option datatype="boolean">uniquetitle</bcf:option>
    <bcf:option datatype="boolean">uniquebaretitle</bcf:option>
    <bcf:option datatype="boolean">uniquework</bcf:option>
    <bcf:option datatype="boolean">uniqueprimaryauthor</bcf:option>
    <bcf:option datatype="boolean">useprefix</bcf:option>
    <bcf:option datatype="boolean">skipbib</bcf:option>
    <bcf:option datatype="boolean">skipbiblist</bcf:option>
    <bcf:option datatype="boolean">skiplab</bcf:option>
    <bcf:option datatype="boolean">dataonly</bcf:option>
    <bcf:option datatype="boolean">skiplos</bcf:option>
    <bcf:option datatype="boolean">labelyear</bcf:option>
    <bcf:option datatype="boolean">isbn</bcf:option>
    <bcf:option datatype="boolean">url</bcf:option>
    <bcf:option datatype="boolean">doi</bcf:option>
    <bcf:option datatype="boolean">eprint</bcf:option>
    <bcf:option datatype="boolean">related</bcf:option>
    <bcf:option datatype="boolean">subentry</bcf:option>
    <bcf:option datatype="boolean">subentrycomp</bcf:option>
    <bcf:option datatype="boolean">bibtexcaseprotection</bcf:option>
    <bcf:option datatype="xml">labelalphatemplate</bcf:option>
    <bcf:option datatype="xml">translit</bcf:option>
    <bcf:option datatype="xml">sortexclusion</bcf:option>
    <bcf:option datatype="xml">sortinclusion</bcf:option>
    <bcf:option datatype="xml">labelnamespec</bcf:option>
    <bcf:option datatype="xml">labeltitlespec</bcf:option>
    <bcf:option datatype="xml">labeldatespec</bcf:option>
    <bcf:option datatype="integer">maxnames</bcf:option>
    <bcf:option datatype="integer">minnames</bcf:option>
    <bcf:option datatype="integer">maxbibnames</bcf:option>
    <bcf:option datatype="integer">minbibnames</bcf:option>
    <bcf:option datatype="integer">maxcitenames</bcf:option>
    <bcf:option datatype="integer">mincitenames</bcf:option>
    <bcf:option datatype="integer">maxsortnames</bcf:option>
    <bcf:option datatype="integer">minsortnames</bcf:option>
    <bcf:option datatype="integer">maxitems</bcf:option>
    <bcf:option datatype="integer">minitems</bcf:option>
    <bcf:option datatype="integer">maxalphanames</bcf:option>
    <bcf:option datatype="integer">minalphanames</bcf:option>
  </bcf:optionscope>
  <bcf:optionscope type="ENTRY">
    <bcf:option datatype="string">noinherit</bcf:option>
    <bcf:option datatype="string" backendin="sortingnamekeytemplatename,uniquenametemplatename,labelalphanametemplatename">nametemplates</bcf:option>
    <bcf:option datatype="string" backendout="1">labelalphanametemplatename</bcf:option>
    <bcf:option datatype="string" backendout="1">uniquenametemplatename</bcf:option>
    <bcf:option datatype="string" backendout="1">sortingnamekeytemplatename</bcf:option>
    <bcf:option datatype="string">presort</bcf:option>
    <bcf:option datatype="string" backendout="1">indexing</bcf:option>
    <bcf:option datatype="string" backendout="1">citetracker</bcf:option>
    <bcf:option datatype="string" backendout="1">ibidtracker</bcf:option>
    <bcf:option datatype="string" backendout="1">idemtracker</bcf:option>
    <bcf:option datatype="string" backendout="1">opcittracker</bcf:option>
    <bcf:option datatype="string" backendout="1">loccittracker</bcf:option>
    <bcf:option datatype="string">uniquelist</bcf:option>
    <bcf:option datatype="string">uniquename</bcf:option>
    <bcf:option datatype="boolean" backendout="1">familyinits</bcf:option>
    <bcf:option datatype="boolean" backendout="1">giveninits</bcf:option>
    <bcf:option datatype="boolean" backendout="1">prefixinits</bcf:option>
    <bcf:option datatype="boolean" backendout="1">suffixinits</bcf:option>
    <bcf:option datatype="boolean" backendout="1">useafterword</bcf:option>
    <bcf:option datatype="boolean" backendout="1">useannotator</bcf:option>
    <bcf:option datatype="boolean" backendout="1">useauthor</bcf:option>
    <bcf:option datatype="boolean" backendout="1">usebookauthor</bcf:option>
    <bcf:option datatype="boolean" backendout="1">usecommentator</bcf:option>
    <bcf:option datatype="boolean" backendout="1">useeditor</bcf:option>
    <bcf:option datatype="boolean" backendout="1">useeditora</bcf:option>
    <bcf:option datatype="boolean" backendout="1">useeditorb</bcf:option>
    <bcf:option datatype="boolean" backendout="1">useeditorc</bcf:option>
    <bcf:option datatype="boolean" backendout="1">useforeword</bcf:option>
    <bcf:option datatype="boolean" backendout="1">useholder</bcf:option>
    <bcf:option datatype="boolean" backendout="1">useintroduction</bcf:option>
    <bcf:option datatype="boolean" backendout="1">usenamea</bcf:option>
    <bcf:option datatype="boolean" backendout="1">usenameb</bcf:option>
    <bcf:option datatype="boolean" backendout="1">usenamec</bcf:option>
    <bcf:option datatype="boolean" backendout="1">usetranslator</bcf:option>
    <bcf:option datatype="boolean" backendout="1">useshortauthor</bcf:option>
    <bcf:option datatype="boolean" backendout="1">useshorteditor</bcf:option>
    <bcf:option datatype="boolean" backendout="1">terseinits</bcf:option>
    <bcf:option datatype="boolean" backendout="1">abbreviate</bcf:option>
    <bcf:option datatype="boolean" backendout="1">dateabbrev</bcf:option>
    <bcf:option datatype="boolean" backendout="1">clearlang</bcf:option>
    <bcf:option datatype="boolean" backendout="1">labelnumber</bcf:option>
    <bcf:option datatype="boolean" backendout="1">labelalpha</bcf:option>
    <bcf:option datatype="boolean" backendout="1">labeltitle</bcf:option>
    <bcf:option datatype="boolean" backendout="1">labeltitleyear</bcf:option>
    <bcf:option datatype="boolean" backendout="1">labeldateparts</bcf:option>
    <bcf:option datatype="boolean">nohashothers</bcf:option>
    <bcf:option datatype="boolean">nosortothers</bcf:option>
    <bcf:option datatype="boolean">noroman</bcf:option>
    <bcf:option datatype="boolean">singletitle</bcf:option>
    <bcf:option datatype="boolean">uniquetitle</bcf:option>
    <bcf:option datatype="boolean">uniquebaretitle</bcf:option>
    <bcf:option datatype="boolean">uniquework</bcf:option>
    <bcf:option datatype="boolean">uniqueprimaryauthor</bcf:option>
    <bcf:option datatype="boolean" backendout="1">useprefix</bcf:option>
    <bcf:option datatype="boolean" backendout="1">skipbib</bcf:option>
    <bcf:option datatype="boolean" backendout="1">skipbiblist</bcf:option>
    <bcf:option datatype="boolean" backendout="1">skiplab</bcf:option>
    <bcf:option datatype="boolean" backendin="uniquename=false,uniquelist=false,skipbib=true,skipbiblist=true,skiplab=true">dataonly</bcf:option>
    <bcf:option datatype="boolean" backendout="1">skiplos</bcf:option>
    <bcf:option datatype="boolean" backendout="1">isbn</bcf:option>
    <bcf:option datatype="boolean" backendout="1">url</bcf:option>
    <bcf:option datatype="boolean" backendout="1">doi</bcf:option>
    <bcf:option datatype="boolean" backendout="1">eprint</bcf:option>
    <bcf:option datatype="boolean" backendout="1">related</bcf:option>
    <bcf:option datatype="boolean" backendout="1">subentry</bcf:option>
    <bcf:option datatype="boolean" backendout="1">subentrycomp</bcf:option>
    <bcf:option datatype="boolean" backendout="1">bibtexcaseprotection</bcf:option>
    <bcf:option datatype="integer" backendin="maxcitenames,maxbibnames,maxsortnames">maxnames</bcf:option>
    <bcf:option datatype="integer" backendin="mincitenames,minbibnames,minsortnames">minnames</bcf:option>
    <bcf:option datatype="integer" backendout="1">maxbibnames</bcf:option>
    <bcf:option datatype="integer" backendout="1">minbibnames</bcf:option>
    <bcf:option datatype="integer" backendout="1">maxcitenames</bcf:option>
    <bcf:option datatype="integer" backendout="1">mincitenames</bcf:option>
    <bcf:option datatype="integer" backendout="1">maxsortnames</bcf:option>
    <bcf:option datatype="integer" backendout="1">minsortnames</bcf:option>
    <bcf:option datatype="integer" backendout="1">maxitems</bcf:option>
    <bcf:option datatype="integer" backendout="1">minitems</bcf:option>
    <bcf:option datatype="integer" backendout="1">maxalphanames</bcf:option>
    <bcf:option datatype="integer" backendout="1">minalphanames</bcf:option>
  </bcf:optionscope>
  <bcf:optionscope type="NAMELIST">
    <bcf:option datatype="string" backendin="sortingnamekeytemplatename,uniquenametemplatename,labelalphanametemplatename">nametemplates</bcf:option>
    <bcf:option datatype="string" backendout="1">labelalphanametemplatename</bcf:option>
    <bcf:option datatype="string" backendout="1">uniquenametemplatename</bcf:option>
    <bcf:option datatype="string" backendout="1">sortingnamekeytemplatename</bcf:option>
    <bcf:option datatype="string">uniquelist</bcf:option>
    <bcf:option datatype="string">uniquename</bcf:option>
    <bcf:option datatype="boolean" backendout="1">familyinits</bcf:option>
    <bcf:option datatype="boolean" backendout="1">giveninits</bcf:option>
    <bcf:option datatype="boolean" backendout="1">prefixinits</bcf:option>
    <bcf:option datatype="boolean" backendout="1">suffixinits</bcf:option>
    <bcf:option datatype="boolean" backendout="1">terseinits</bcf:option>
    <bcf:option datatype="boolean">nohashothers</bcf:option>
    <bcf:option datatype="boolean">nosortothers</bcf:option>
    <bcf:option datatype="boolean" backendout="1">useprefix</bcf:option>
  </bcf:optionscope>
  <bcf:optionscope type="NAME">
    <bcf:option datatype="string" backendin="sortingnamekeytemplatename,uniquenametemplatename,labelalphanametemplatename">nametemplates</bcf:option>
    <bcf:option datatype="string" backendout="1">labelalphanametemplatename</bcf:option>
    <bcf:option datatype="string" backendout="1">uniquenametemplatename</bcf:option>
    <bcf:option datatype="string" backendout="1">sortingnamekeytemplatename</bcf:option>
    <bcf:option datatype="string">uniquename</bcf:option>
    <bcf:option datatype="boolean" backendout="1">familyinits</bcf:option>
    <bcf:option datatype="boolean" backendout="1">giveninits</bcf:option>
    <bcf:option datatype="boolean" backendout="1">prefixinits</bcf:option>
    <bcf:option datatype="boolean" backendout="1">suffixinits</bcf:option>
    <bcf:option datatype="boolean" backendout="1">terseinits</bcf:option>
    <bcf:option datatype="boolean" backendout="1">useprefix</bcf:option>
  </bcf:optionscope>
  <!-- DATAFIELDSETS -->
  <bcf:datafieldset name="setnames">
    <bcf:member datatype="name" fieldtype="list"/>
  </bcf:datafieldset>
  <bcf:datafieldset name="settitles">
    <bcf:member field="title"/>
    <bcf:member field="booktitle"/>
    <bcf:member field="eventtitle"/>
    <bcf:member field="issuetitle"/>
    <bcf:member field="journaltitle"/>
    <bcf:member field="maintitle"/>
    <bcf:member field="origtitle"/>
  </bcf:datafieldset>
  <!-- SOURCEMAP -->
  <bcf:sourcemap>
    <bcf:maps datatype="bibtex" level="driver">
      <bcf:map>
        <bcf:map_step map_field_set="day" map_null="1"/>
      </bcf:map>
      <bcf:map>
        <bcf:map_step map_type_source="conference" map_type_target="inproceedings"/>
        <bcf:map_step map_type_source="electronic" map_type_target="online"/>
        <bcf:map_step map_type_source="www" map_type_target="online"/>
      </bcf:map>
      <bcf:map>
        <bcf:map_step map_type_source="mastersthesis" map_type_target="thesis" map_final="1"/>
        <bcf:map_step map_field_set="type" map_field_value="mathesis"/>
      </bcf:map>
      <bcf:map>
        <bcf:map_step map_type_source="phdthesis" map_type_target="thesis" map_final="1"/>
        <bcf:map_step map_field_set="type" map_field_value="phdthesis"/>
      </bcf:map>
      <bcf:map>
        <bcf:map_step map_type_source="techreport" map_type_target="report" map_final="1"/>
        <bcf:map_step map_field_set="type" map_field_value="techreport"/>
      </bcf:map>
      <bcf:map>
        <bcf:map_step map_field_source="hyphenation" map_field_target="langid"/>
        <bcf:map_step map_field_source="address" map_field_target="location"/>
        <bcf:map_step map_field_source="school" map_field_target="institution"/>
        <bcf:map_step map_field_source="annote" map_field_target="annotation"/>
        <bcf:map_step map_field_source="archiveprefix" map_field_target="eprinttype"/>
        <bcf:map_step map_field_source="journal" map_field_target="journaltitle"/>
        <bcf:map_step map_field_source="primaryclass" map_field_target="eprintclass"/>
        <bcf:map_step map_field_source="key" map_field_target="sortkey"/>
        <bcf:map_step map_field_source="pdf" map_field_target="file"/>
      </bcf:map>
    </bcf:maps>
  </bcf:sourcemap>
  <!-- LABELALPHA NAME TEMPLATE -->
  <bcf:labelalphanametemplate name="global">
    <bcf:namepart order="1" use="1" pre="1" substring_width="1" substring_compound="1">prefix</bcf:namepart>
    <bcf:namepart order="2">family</bcf:namepart>
  </bcf:labelalphanametemplate>
  <!-- LABELALPHA TEMPLATE -->
  <bcf:labelalphatemplate type="global">
    <bcf:labelelement order="1">
      <bcf:labelpart final="1">shorthand</bcf:labelpart>
      <bcf:labelpart>label</bcf:labelpart>
      <bcf:labelpart substring_width="3" substring_side="left" ifnames="1">labelname</bcf:labelpart>
      <bcf:labelpart substring_width="1" substring_side="left">labelname</bcf:labelpart>
    </bcf:labelelement>
    <bcf:labelelement order="2">
      <bcf:labelpart substring_width="2" substring_side="right">year</bcf:labelpart>
    </bcf:labelelement>
  </bcf:labelalphatemplate>
  <!-- EXTRADATE -->
  <bcf:extradatespec>
    <bcf:scope>
      <bcf:field order="1">labelyear</bcf:field>
      <bcf:field order="2">year</bcf:field>
    </bcf:scope>
  </bcf:extradatespec>
  <!-- INHERITANCE -->
  <bcf:inheritance>
    <bcf:defaults inherit_all="true" override_target="false">
    </bcf:defaults>
    <bcf:inherit>
      <bcf:type_pair source="mvbook" target="inbook"/>
      <bcf:type_pair source="mvbook" target="bookinbook"/>
      <bcf:type_pair source="mvbook" target="suppbook"/>
      <bcf:type_pair source="book" target="inbook"/>
      <bcf:type_pair source="book" target="bookinbook"/>
      <bcf:type_pair source="book" target="suppbook"/>
      <bcf:field source="author" target="author"/>
      <bcf:field source="author" target="bookauthor"/>
    </bcf:inherit>
    <bcf:inherit>
      <bcf:type_pair source="mvbook" target="book"/>
      <bcf:type_pair source="mvbook" target="inbook"/>
      <bcf:type_pair source="mvbook" target="bookinbook"/>
      <bcf:type_pair source="mvbook" target="suppbook"/>
      <bcf:field source="title" target="maintitle"/>
      <bcf:field source="subtitle" target="mainsubtitle"/>
      <bcf:field source="titleaddon" target="maintitleaddon"/>
      <bcf:field source="shorttitle" skip="true"/>
      <bcf:field source="sorttitle" skip="true"/>
      <bcf:field source="indextitle" skip="true"/>
      <bcf:field source="indexsorttitle" skip="true"/>
    </bcf:inherit>
    <bcf:inherit>
      <bcf:type_pair source="mvcollection" target="collection"/>
      <bcf:type_pair source="mvcollection" target="reference"/>
      <bcf:type_pair source="mvcollection" target="incollection"/>
      <bcf:type_pair source="mvcollection" target="inreference"/>
      <bcf:type_pair source="mvcollection" target="suppcollection"/>
      <bcf:type_pair source="mvreference" target="collection"/>
      <bcf:type_pair source="mvreference" target="reference"/>
      <bcf:type_pair source="mvreference" target="incollection"/>
      <bcf:type_pair source="mvreference" target="inreference"/>
      <bcf:type_pair source="mvreference" target="suppcollection"/>
      <bcf:field source="title" target="maintitle"/>
      <bcf:field source="subtitle" target="mainsubtitle"/>
      <bcf:field source="titleaddon" target="maintitleaddon"/>
      <bcf:field source="shorttitle" skip="true"/>
      <bcf:field source="sorttitle" skip="true"/>
      <bcf:field source="indextitle" skip="true"/>
      <bcf:field source="indexsorttitle" skip="true"/>
    </bcf:inherit>
    <bcf:inherit>
      <bcf:type_pair source="mvproceedings" target="proceedings"/>
      <bcf:type_pair source="mvproceedings" target="inproceedings"/>
      <bcf:field source="title" target="maintitle"/>
      <bcf:field source="subtitle" target="mainsubtitle"/>
      <bcf:field source="titleaddon" target="maintitleaddon"/>
      <bcf:field source="shorttitle" skip="true"/>
      <bcf:field source="sorttitle" skip="true"/>
      <bcf:field source="indextitle" skip="true"/>
      <bcf:field source="indexsorttitle" skip="true"/>
    </bcf:inherit>
    <bcf:inherit>
      <bcf:type_pair source="book" target="inbook"/>
      <bcf:type_pair source="book" target="bookinbook"/>
      <bcf:type_pair source="book" target="suppbook"/>
      <bcf:field source="title" target="booktitle"/>
      <bcf:field source="subtitle" target="booksubtitle"/>
      <bcf:field source="titleaddon" target="booktitleaddon"/>
      <bcf:field source="shorttitle" skip="true"/>
      <bcf:field source="sorttitle" skip="true"/>
      <bcf:field source="indextitle" skip="true"/>
      <bcf:field source="indexsorttitle" skip="true"/>
    </bcf:inherit>
    <bcf:inherit>
      <bcf:type_pair source="collection" target="incollection"/>
      <bcf:type_pair source="collection" target="inreference"/>
      <bcf:type_pair source="collection" target="suppcollection"/>
      <bcf:type_pair source="reference" target="incollection"/>
      <bcf:type_pair source="reference" target="inreference"/>
      <bcf:type_pair source="reference" target="suppcollection"/>
      <bcf:field source="title" target="booktitle"/>
      <bcf:field source="subtitle" target="booksubtitle"/>
      <bcf:field source="titleaddon" target="booktitleaddon"/>
      <bcf:field source="shorttitle" skip="true"/>
      <bcf:field source="sorttitle" skip="true"/>
      <bcf:field source="indextitle" skip="true"/>
      <bcf:field source="indexsorttitle" skip="true"/>
    </bcf:inherit>
    <bcf:inherit>
      <bcf:type_pair source="proceedings" target="inproceedings"/>
      <bcf:field source="title" target="booktitle"/>
      <bcf:field source="subtitle" target="booksubtitle"/>
      <bcf:field source="titleaddon" target="booktitleaddon"/>
      <bcf:field source="shorttitle" skip="true"/>
      <bcf:field source="sorttitle" skip="true"/>
      <bcf:field source="indextitle" skip="true"/>
      <bcf:field source="indexsorttitle" skip="true"/>
    </bcf:inherit>
    <bcf:inherit>
      <bcf:type_pair source="periodical" target="article"/>
      <bcf:type_pair source="periodical" target="suppperiodical"/>
      <bcf:field source="title" target="journaltitle"/>
      <bcf:field source="subtitle" target="journalsubtitle"/>
      <bcf:field source="titleaddon" target="journaltitleaddon"/>
      <bcf:field source="shorttitle" skip="true"/>
      <bcf:field source="sorttitle" skip="true"/>
      <bcf:field source="indextitle" skip="true"/>
      <bcf:field source="indexsorttitle" skip="true"/>
    </bcf:inherit>
    <bcf:inherit>
      <bcf:type_pair source="*" target="*"/>
      <bcf:field source="ids" skip="true"/>
      <bcf:field source="crossref" skip="true"/>
      <bcf:field source="xref" skip="true"/>
      <bcf:field source="entryset" skip="true"/>
      <bcf:field source="entrysubtype" skip="true"/>
      <bcf:field source="execute" skip="true"/>
      <bcf:field source="label" skip="true"/>
      <bcf:field source="options" skip="true"/>
      <bcf:field source="presort" skip="true"/>
      <bcf:field source="related" skip="true"/>
      <bcf:field source="relatedoptions" skip="true"/>
      <bcf:field source="relatedstring" skip="true"/>
      <bcf:field source="relatedtype" skip="true"/>
      <bcf:field source="shorthand" skip="true"/>
      <bcf:field source="shorthandintro" skip="true"/>
      <bcf:field source="sortkey" skip="true"/>
    </bcf:inherit>
  </bcf:inheritance>
  <!-- UNIQUENAME TEMPLATES -->
  <bcf:uniquenametemplate name="global">
    <bcf:namepart order="1" use="1" base="1">prefix</bcf:namepart>
    <bcf:namepart order="2" base="1">family</bcf:namepart>
    <bcf:namepart order="3">given</bcf:namepart>
  </bcf:uniquenametemplate>
  <!-- SORTING NAME KEY TEMPLATES -->
  <bcf:sortingnamekeytemplate name="global">
    <bcf:keypart order="1">
      <bcf:part type="namepart" order="1" use="1">prefix</bcf:part>
      <bcf:part type="namepart" order="2">family</bcf:part>
    </bcf:keypart>
    <bcf:keypart order="2">
      <bcf:part type="namepart" order="1">given</bcf:part>
    </bcf:keypart>
    <bcf:keypart order="3">
      <bcf:part type="namepart" order="1">suffix</bcf:part>
    </bcf:keypart>
    <bcf:keypart order="4">
      <bcf:part type="namepart" order="1" use="0">prefix</bcf:part>
    </bcf:keypart>
  </bcf:sortingnamekeytemplate>
  <bcf:presort>mm</bcf:presort>
  <!-- DATA MODEL -->
  <bcf:datamodel>
    <bcf:constants>
      <bcf:constant type="list" name="gender">sf,sm,sn,pf,pm,pn,pp</bcf:constant>
      <bcf:constant type="list" name="nameparts">family,given,prefix,suffix</bcf:constant>
      <bcf:constant type="list" name="optiondatatypes">boolean,integer,string,xml</bcf:constant>
      <bcf:constant type="list" name="multiscriptforms">default,transliteration,transcription,translation</bcf:constant>
    </bcf:constants>
    <bcf:entrytypes>
      <bcf:entrytype>article</bcf:entrytype>
      <bcf:entrytype>artwork</bcf:entrytype>
      <bcf:entrytype>audio</bcf:entrytype>
      <bcf:entrytype>bibnote</bcf:entrytype>
      <bcf:entrytype>book</bcf:entrytype>
      <bcf:entrytype>bookinbook</bcf:entrytype>
      <bcf:entrytype>booklet</bcf:entrytype>
      <bcf:entrytype>collection</bcf:entrytype>
      <bcf:entrytype>commentary</bcf:entrytype>
      <bcf:entrytype>customa</bcf:entrytype>
      <bcf:entrytype>customb</bcf:entrytype>
      <bcf:entrytype>customc</bcf:entrytype>
      <bcf:entrytype>customd</bcf:entrytype>
      <bcf:entrytype>custome</bcf:entrytype>
      <bcf:entrytype>customf</bcf:entrytype>
      <bcf:entrytype>dataset</bcf:entrytype>
      <bcf:entrytype>inbook</bcf:entrytype>
      <bcf:entrytype>incollection</bcf:entrytype>
      <bcf:entrytype>inproceedings</bcf:entrytype>
      <bcf:entrytype>inreference</bcf:entrytype>
      <bcf:entrytype>image</bcf:entrytype>
      <bcf:entrytype>jurisdiction</bcf:entrytype>
      <bcf:entrytype>legal</bcf:entrytype>
      <bcf:entrytype>legislation</bcf:entrytype>
      <bcf:entrytype>letter</bcf:entrytype>
      <bcf:entrytype>manual</bcf:entrytype>
      <bcf:entrytype>misc</bcf:entrytype>
      <bcf:entrytype>movie</bcf:entrytype>
      <bcf:entrytype>music</bcf:entrytype>
      <bcf:entrytype>mvcollection</bcf:entrytype>
      <bcf:entrytype>mvreference</bcf:entrytype>
      <bcf:entrytype>mvproceedings</bcf:entrytype>
      <bcf:entrytype>mvbook</bcf:entrytype>
      <bcf:entrytype>online</bcf:entrytype>
      <bcf:entrytype>patent</bcf:entrytype>
      <bcf:entrytype>performance</bcf:entrytype>
      <bcf:entrytype>periodical</bcf:entrytype>
      <bcf:entrytype>proceedings</bcf:entrytype>
      <bcf:entrytype>reference</bcf:entrytype>
      <bcf:entrytype>report</bcf:entrytype>
      <bcf:entrytype>review</bcf:entrytype>
      <bcf:entrytype>set</bcf:entrytype>
      <bcf:entrytype>software</bcf:entrytype>
      <bcf:entrytype>standard</bcf:entrytype>
      <bcf:entrytype>suppbook</bcf:entrytype>
      <bcf:entrytype>suppcollection</bcf:entrytype>
      <bcf:entrytype>suppperiodical</bcf:entrytype>
      <bcf:entrytype>thesis</bcf:entrytype>
      <bcf:entrytype>unpublished</bcf:entrytype>
      <bcf:entrytype>video</bcf:entrytype>
      <bcf:entrytype skip_output="true">xdata</bcf:entrytype>
    </bcf:entrytypes>
    <bcf:fields>
      <bcf:field fieldtype="field" datatype="integer">sortyear</bcf:field>
      <bcf:field fieldtype="field" datatype="integer">volume</bcf:field>
      <bcf:field fieldtype="field" datatype="integer">volumes</bcf:field>
      <bcf:field fieldtype="field" datatype="literal">abstract</bcf:field>
      <bcf:field fieldtype="field" datatype="literal">addendum</bcf:field>
      <bcf:field fieldtype="field" datatype="literal">annotation</bcf:field>
      <bcf:field fieldtype="field" datatype="literal">booksubtitle</bcf:field>
      <bcf:field fieldtype="field" datatype="literal">booktitle</bcf:field>
      <bcf:field fieldtype="field" datatype="literal">booktitleaddon</bcf:field>
      <bcf:field fieldtype="field" datatype="literal">chapter</bcf:field>
      <bcf:field fieldtype="field" datatype="literal">edition</bcf:field>
      <bcf:field fieldtype="field" datatype="literal">eid</bcf:field>
      <bcf:field fieldtype="field" datatype="literal">entrysubtype</bcf:field>
      <bcf:field fieldtype="field" datatype="literal">eprintclass</bcf:field>
      <bcf:field fieldtype="field" datatype="literal">eprinttype</bcf:field>
      <bcf:field fieldtype="field" datatype="literal">eventtitle</bcf:field>
      <bcf:field fieldtype="field" datatype="literal">eventtitleaddon</bcf:field>
      <bcf:field fieldtype="field" datatype="literal">gender</bcf:field>
      <bcf:field fieldtype="field" datatype="literal">howpublished</bcf:field>
      <bcf:field fieldtype="field" datatype="literal">indexsorttitle</bcf:field>
      <bcf:field fieldtype="field" datatype="literal">indextitle</bcf:field>
      <bcf:field fieldtype="field" datatype="literal">isan</bcf:field>
      <bcf:field fieldtype="field" datatype="literal">isbn</bcf:field>
      <bcf:field fieldtype="field" datatype="literal">ismn</bcf:field>
      <bcf:field fieldtype="field" datatype="literal">isrn</bcf:field>
      <bcf:field fieldtype="field" datatype="literal">issn</bcf:field>
      <bcf:field fieldtype="field" datatype="literal">issue</bcf:field>
      <bcf:field fieldtype="field" datatype="literal">issuesubtitle</bcf:field>
      <bcf:field fieldtype="field" datatype="literal">issuetitle</bcf:field>
      <bcf:field fieldtype="field" datatype="literal">issuetitleaddon</bcf:field>
      <bcf:field fieldtype="field" datatype="literal">iswc</bcf:field>
      <bcf:field fieldtype="field" datatype="literal">journalsubtitle</bcf:field>
      <bcf:field fieldtype="field" datatype="literal">journaltitle</bcf:field>
      <bcf:field fieldtype="field" datatype="literal">journaltitleaddon</bcf:field>
      <bcf:field fieldtype="field" datatype="literal">label</bcf:field>
      <bcf:field fieldtype="field" datatype="literal">langid</bcf:field>
      <bcf:field fieldtype="field" datatype="literal">langidopts</bcf:field>
      <bcf:field fieldtype="field" datatype="literal">library</bcf:field>
      <bcf:field fieldtype="field" datatype="literal">mainsubtitle</bcf:field>
      <bcf:field fieldtype="field" datatype="literal">maintitle</bcf:field>
      <bcf:field fieldtype="field" datatype="literal">maintitleaddon</bcf:field>
      <bcf:field fieldtype="field" datatype="literal">nameaddon</bcf:field>
      <bcf:field fieldtype="field" datatype="literal">note</bcf:field>
      <bcf:field fieldtype="field" datatype="literal">number</bcf:field>
      <bcf:field fieldtype="field" datatype="literal">origtitle</bcf:field>
      <bcf:field fieldtype="field" datatype="literal">pagetotal</bcf:field>
      <bcf:field fieldtype="field" datatype="literal">part</bcf:field>
      <bcf:field fieldtype="field" datatype="literal">relatedstring</bcf:field>
      <bcf:field fieldtype="field" datatype="literal">relatedtype</bcf:field>
      <bcf:field fieldtype="field" datatype="literal">reprinttitle</bcf:field>
      <bcf:field fieldtype="field" datatype="literal">series</bcf:field>
      <bcf:field fieldtype="field" datatype="literal">shorthandintro</bcf:field>
      <bcf:field fieldtype="field" datatype="literal">subtitle</bcf:field>
      <bcf:field fieldtype="field" datatype="literal">title</bcf:field>
      <bcf:field fieldtype="field" datatype="literal">titleaddon</bcf:field>
      <bcf:field fieldtype="field" datatype="literal">usera</bcf:field>
      <bcf:field fieldtype="field" datatype="literal">userb</bcf:field>
      <bcf:field fieldtype="field" datatype="literal">userc</bcf:field>
      <bcf:field fieldtype="field" datatype="literal">userd</bcf:field>
      <bcf:field fieldtype="field" datatype="literal">usere</bcf:field>
      <bcf:field fieldtype="field" datatype="literal">userf</bcf:field>
      <bcf:field fieldtype="field" datatype="literal">venue</bcf:field>
      <bcf:field fieldtype="field" datatype="literal">version</bcf:field>
      <bcf:field fieldtype="field" datatype="literal" label="true">shorthand</bcf:field>
      <bcf:field fieldtype="field" datatype="literal" label="true">shortjournal</bcf:field>
      <bcf:field fieldtype="field" datatype="literal" label="true">shortseries</bcf:field>
      <bcf:field fieldtype="field" datatype="literal" label="true">shorttitle</bcf:field>
      <bcf:field fieldtype="field" datatype="literal" skip_output="true">sorttitle</bcf:field>
      <bcf:field fieldtype="field" datatype="literal" skip_output="true">sortshorthand</bcf:field>
      <bcf:field fieldtype="field" datatype="literal" skip_output="true">sortkey</bcf:field>
      <bcf:field fieldtype="field" datatype="literal" skip_output="true">presort</bcf:field>
      <bcf:field fieldtype="list" datatype="literal">institution</bcf:field>
      <bcf:field fieldtype="list" datatype="literal">lista</bcf:field>
      <bcf:field fieldtype="list" datatype="literal">listb</bcf:field>
      <bcf:field fieldtype="list" datatype="literal">listc</bcf:field>
      <bcf:field fieldtype="list" datatype="literal">listd</bcf:field>
      <bcf:field fieldtype="list" datatype="literal">liste</bcf:field>
      <bcf:field fieldtype="list" datatype="literal">listf</bcf:field>
      <bcf:field fieldtype="list" datatype="literal">location</bcf:field>
      <bcf:field fieldtype="list" datatype="literal">organization</bcf:field>
      <bcf:field fieldtype="list" datatype="literal">origlocation</bcf:field>
      <bcf:field fieldtype="list" datatype="literal">origpublisher</bcf:field>
      <bcf:field fieldtype="list" datatype="literal">publisher</bcf:field>
      <bcf:field fieldtype="list" datatype="name">afterword</bcf:field>
      <bcf:field fieldtype="list" datatype="name">annotator</bcf:field>
      <bcf:field fieldtype="list" datatype="name">author</bcf:field>
      <bcf:field fieldtype="list" datatype="name">bookauthor</bcf:field>
      <bcf:field fieldtype="list" datatype="name">commentator</bcf:field>
      <bcf:field fieldtype="list" datatype="name">editor</bcf:field>
      <bcf:field fieldtype="list" datatype="name">editora</bcf:field>
      <bcf:field fieldtype="list" datatype="name">editorb</bcf:field>
      <bcf:field fieldtype="list" datatype="name">editorc</bcf:field>
      <bcf:field fieldtype="list" datatype="name">foreword</bcf:field>
      <bcf:field fieldtype="list" datatype="name">holder</bcf:field>
      <bcf:field fieldtype="list" datatype="name">introduction</bcf:field>
      <bcf:field fieldtype="list" datatype="name">namea</bcf:field>
      <bcf:field fieldtype="list" datatype="name">nameb</bcf:field>
      <bcf:field fieldtype="list" datatype="name">namec</bcf:field>
      <bcf:field fieldtype="list" datatype="name">translator</bcf:field>
      <bcf:field fieldtype="list" datatype="name" label="true">shortauthor</bcf:field>
      <bcf:field fieldtype="list" datatype="name" label="true">shorteditor</bcf:field>
      <bcf:field fieldtype="list" datatype="name" skip_output="true">sortname</bcf:field>
      <bcf:field fieldtype="field" datatype="key">authortype</bcf:field>
      <bcf:field fieldtype="field" datatype="key">editoratype</bcf:field>
      <bcf:field fieldtype="field" datatype="key">editorbtype</bcf:field>
      <bcf:field fieldtype="field" datatype="key">editorctype</bcf:field>
      <bcf:field fieldtype="field" datatype="key">editortype</bcf:field>
      <bcf:field fieldtype="field" datatype="key">bookpagination</bcf:field>
      <bcf:field fieldtype="field" datatype="key">nameatype</bcf:field>
      <bcf:field fieldtype="field" datatype="key">namebtype</bcf:field>
      <bcf:field fieldtype="field" datatype="key">namectype</bcf:field>
      <bcf:field fieldtype="field" datatype="key">pagination</bcf:field>
      <bcf:field fieldtype="field" datatype="key">pubstate</bcf:field>
      <bcf:field fieldtype="field" datatype="key">type</bcf:field>
      <bcf:field fieldtype="list" datatype="key">language</bcf:field>
      <bcf:field fieldtype="list" datatype="key">origlanguage</bcf:field>
      <bcf:field fieldtype="field" datatype="entrykey">crossref</bcf:field>
      <bcf:field fieldtype="field" datatype="entrykey">xref</bcf:field>
      <bcf:field fieldtype="field" datatype="date" skip_output="true">date</bcf:field>
      <bcf:field fieldtype="field" datatype="datepart" nullok="true">endyear</bcf:field>
      <bcf:field fieldtype="field" datatype="datepart" nullok="true">year</bcf:field>
      <bcf:field fieldtype="field" datatype="datepart">month</bcf:field>
      <bcf:field fieldtype="field" datatype="datepart">day</bcf:field>
      <bcf:field fieldtype="field" datatype="datepart">hour</bcf:field>
      <bcf:field fieldtype="field" datatype="datepart">minute</bcf:field>
      <bcf:field fieldtype="field" datatype="datepart">second</bcf:field>
      <bcf:field fieldtype="field" datatype="datepart">timezone</bcf:field>
      <bcf:field fieldtype="field" datatype="datepart">yeardivision</bcf:field>
      <bcf:field fieldtype="field" datatype="datepart">endmonth</bcf:field>
      <bcf:field fieldtype="field" datatype="datepart">endday</bcf:field>
      <bcf:field fieldtype="field" datatype="datepart">endhour</bcf:field>
      <bcf:field fieldtype="field" datatype="datepart">endminute</bcf:field>
      <bcf:field fieldtype="field" datatype="datepart">endsecond</bcf:field>
      <bcf:field fieldtype="field" datatype="datepart">endtimezone</bcf:field>
      <bcf:field fieldtype="field" datatype="datepart">endyeardivision</bcf:field>
      <bcf:field fieldtype="field" datatype="date" skip_output="true">eventdate</bcf:field>
      <bcf:field fieldtype="field" datatype="datepart" nullok="true">eventendyear</bcf:field>
      <bcf:field fieldtype="field" datatype="datepart" nullok="true">eventyear</bcf:field>
      <bcf:field fieldtype="field" datatype="datepart">eventmonth</bcf:field>
      <bcf:field fieldtype="field" datatype="datepart">eventday</bcf:field>
      <bcf:field fieldtype="field" datatype="datepart">eventhour</bcf:field>
      <bcf:field fieldtype="field" datatype="datepart">eventminute</bcf:field>
      <bcf:field fieldtype="field" datatype="datepart">eventsecond</bcf:field>
      <bcf:field fieldtype="field" datatype="datepart">eventtimezone</bcf:field>
      <bcf:field fieldtype="field" datatype="datepart">eventyeardivision</bcf:field>
      <bcf:field fieldtype="field" datatype="datepart">eventendmonth</bcf:field>
      <bcf:field fieldtype="field" datatype="datepart">eventendday</bcf:field>
      <bcf:field fieldtype="field" datatype="datepart">eventendhour</bcf:field>
      <bcf:field fieldtype="field" datatype="datepart">eventendminute</bcf:field>
      <bcf:field fieldtype="field" datatype="datepart">eventendsecond</bcf:field>
      <bcf:field fieldtype="field" datatype="datepart">eventendtimezone</bcf:field>
      <bcf:field fieldtype="field" datatype="datepart">eventendyeardivision</bcf:field>
      <bcf:field fieldtype="field" datatype="date" skip_output="true">origdate</bcf:field>
      <bcf:field fieldtype="field" datatype="datepart" nullok="true">origendyear</bcf:field>
      <bcf:field fieldtype="field" datatype="datepart" nullok="true">origyear</bcf:field>
      <bcf:field fieldtype="field" datatype="datepart">origmonth</bcf:field>
      <bcf:field fieldtype="field" datatype="datepart">origday</bcf:field>
      <bcf:field fieldtype="field" datatype="datepart">orighour</bcf:field>
      <bcf:field fieldtype="field" datatype="datepart">origminute</bcf:field>
      <bcf:field fieldtype="field" datatype="datepart">origsecond</bcf:field>
      <bcf:field fieldtype="field" datatype="datepart">origtimezone</bcf:field>
      <bcf:field fieldtype="field" datatype="datepart">origyeardivision</bcf:field>
      <bcf:field fieldtype="field" datatype="datepart">origendmonth</bcf:field>
      <bcf:field fieldtype="field" datatype="datepart">origendday</bcf:field>
      <bcf:field fieldtype="field" datatype="datepart">origendhour</bcf:field>
      <bcf:field fieldtype="field" datatype="datepart">origendminute</bcf:field>
      <bcf:field fieldtype="field" datatype="datepart">origendsecond</bcf:field>
      <bcf:field fieldtype="field" datatype="datepart">origendtimezone</bcf:field>
      <bcf:field fieldtype="field" datatype="datepart">origendyeardivision</bcf:field>
      <bcf:field fieldtype="field" datatype="date" skip_output="true">urldate</bcf:field>
      <bcf:field fieldtype="field" datatype="datepart" nullok="true">urlendyear</bcf:field>
      <bcf:field fieldtype="field" datatype="datepart" nullok="true">urlyear</bcf:field>
      <bcf:field fieldtype="field" datatype="datepart">urlmonth</bcf:field>
      <bcf:field fieldtype="field" datatype="datepart">urlday</bcf:field>
      <bcf:field fieldtype="field" datatype="datepart">urlhour</bcf:field>
      <bcf:field fieldtype="field" datatype="datepart">urlminute</bcf:field>
      <bcf:field fieldtype="field" datatype="datepart">urlsecond</bcf:field>
      <bcf:field fieldtype="field" datatype="datepart">urltimezone</bcf:field>
      <bcf:field fieldtype="field" datatype="datepart">urlyeardivision</bcf:field>
      <bcf:field fieldtype="field" datatype="datepart">urlendmonth</bcf:field>
      <bcf:field fieldtype="field" datatype="datepart">urlendday</bcf:field>
      <bcf:field fieldtype="field" datatype="datepart">urlendhour</bcf:field>
      <bcf:field fieldtype="field" datatype="datepart">urlendminute</bcf:field>
      <bcf:field fieldtype="field" datatype="datepart">urlendsecond</bcf:field>
      <bcf:field fieldtype="field" datatype="datepart">urlendtimezone</bcf:field>
      <bcf:field fieldtype="field" datatype="datepart">urlendyeardivision</bcf:field>
      <bcf:field fieldtype="field" datatype="verbatim">doi</bcf:field>
      <bcf:field fieldtype="field" datatype="verbatim">eprint</bcf:field>
      <bcf:field fieldtype="field" datatype="verbatim">file</bcf:field>
      <bcf:field fieldtype="field" datatype="verbatim">verba</bcf:field>
      <bcf:field fieldtype="field" datatype="verbatim">verbb</bcf:field>
      <bcf:field fieldtype="field" datatype="verbatim">verbc</bcf:field>
      <bcf:field fieldtype="field" datatype="uri">url</bcf:field>
      <bcf:field fieldtype="field" format="xsv" datatype="entrykey" skip_output="true">xdata</bcf:field>
      <bcf:field fieldtype="field" format="xsv" datatype="entrykey" skip_output="true">ids</bcf:field>
      <bcf:field fieldtype="field" format="xsv" datatype="entrykey" skip_output="true">entryset</bcf:field>
      <bcf:field fieldtype="field" format="xsv" datatype="entrykey">related</bcf:field>
      <bcf:field fieldtype="field" format="xsv" datatype="keyword">keywords</bcf:field>
      <bcf:field fieldtype="field" format="xsv" datatype="option" skip_output="true">options</bcf:field>
      <bcf:field fieldtype="field" format="xsv" datatype="option" skip_output="true">relatedoptions</bcf:field>
      <bcf:field fieldtype="field" datatype="range">pages</bcf:field>
      <bcf:field fieldtype="field" datatype="code">execute</bcf:field>
      <bcf:field fieldtype="field" datatype="literal">collaboration</bcf:field>
    </bcf:fields>
    <bcf:entryfields>
      <bcf:field>abstract</bcf:field>
      <bcf:field>annotation</bcf:field>
      <bcf:field>authortype</bcf:field>
      <bcf:field>bookpagination</bcf:field>
      <bcf:field>crossref</bcf:field>
      <bcf:field>day</bcf:field>
      <bcf:field>doi</bcf:field>
      <bcf:field>eprint</bcf:field>
      <bcf:field>eprintclass</bcf:field>
      <bcf:field>eprinttype</bcf:field>
      <bcf:field>endday</bcf:field>
      <bcf:field>endhour</bcf:field>
      <bcf:field>endminute</bcf:field>
      <bcf:field>endmonth</bcf:field>
      <bcf:field>endsecond</bcf:field>
      <bcf:field>endtimezone</bcf:field>
      <bcf:field>endyear</bcf:field>
      <bcf:field>endyeardivision</bcf:field>
      <bcf:field>entryset</bcf:field>
      <bcf:field>entrysubtype</bcf:field>
      <bcf:field>execute</bcf:field>
      <bcf:field>file</bcf:field>
      <bcf:field>gender</bcf:field>
      <bcf:field>hour</bcf:field>
      <bcf:field>ids</bcf:field>
      <bcf:field>indextitle</bcf:field>
      <bcf:field>indexsorttitle</bcf:field>
      <bcf:field>isan</bcf:field>
      <bcf:field>ismn</bcf:field>
      <bcf:field>iswc</bcf:field>
      <bcf:field>keywords</bcf:field>
      <bcf:field>label</bcf:field>
      <bcf:field>langid</bcf:field>
      <bcf:field>langidopts</bcf:field>
      <bcf:field>library</bcf:field>
      <bcf:field>lista</bcf:field>
      <bcf:field>listb</bcf:field>
      <bcf:field>listc</bcf:field>
      <bcf:field>listd</bcf:field>
      <bcf:field>liste</bcf:field>
      <bcf:field>listf</bcf:field>
      <bcf:field>minute</bcf:field>
      <bcf:field>month</bcf:field>
      <bcf:field>namea</bcf:field>
      <bcf:field>nameb</bcf:field>
      <bcf:field>namec</bcf:field>
      <bcf:field>nameatype</bcf:field>
      <bcf:field>namebtype</bcf:field>
      <bcf:field>namectype</bcf:field>
      <bcf:field>nameaddon</bcf:field>
      <bcf:field>options</bcf:field>
      <bcf:field>origday</bcf:field>
      <bcf:field>origendday</bcf:field>
      <bcf:field>origendhour</bcf:field>
      <bcf:field>origendminute</bcf:field>
      <bcf:field>origendmonth</bcf:field>
      <bcf:field>origendsecond</bcf:field>
      <bcf:field>origendtimezone</bcf:field>
      <bcf:field>origendyear</bcf:field>
      <bcf:field>origendyeardivision</bcf:field>
      <bcf:field>orighour</bcf:field>
      <bcf:field>origminute</bcf:field>
      <bcf:field>origmonth</bcf:field>
      <bcf:field>origsecond</bcf:field>
      <bcf:field>origtimezone</bcf:field>
      <bcf:field>origyear</bcf:field>
      <bcf:field>origyeardivision</bcf:field>
      <bcf:field>origlocation</bcf:field>
      <bcf:field>origpublisher</bcf:field>
      <bcf:field>origtitle</bcf:field>
      <bcf:field>pagination</bcf:field>
      <bcf:field>presort</bcf:field>
      <bcf:field>related</bcf:field>
      <bcf:field>relatedoptions</bcf:field>
      <bcf:field>relatedstring</bcf:field>
      <bcf:field>relatedtype</bcf:field>
      <bcf:field>second</bcf:field>
      <bcf:field>shortauthor</bcf:field>
      <bcf:field>shorteditor</bcf:field>
      <bcf:field>shorthand</bcf:field>
      <bcf:field>shorthandintro</bcf:field>
      <bcf:field>shortjournal</bcf:field>
      <bcf:field>shortseries</bcf:field>
      <bcf:field>shorttitle</bcf:field>
      <bcf:field>sortkey</bcf:field>
      <bcf:field>sortname</bcf:field>
      <bcf:field>sortshorthand</bcf:field>
      <bcf:field>sorttitle</bcf:field>
      <bcf:field>sortyear</bcf:field>
      <bcf:field>timezone</bcf:field>
      <bcf:field>url</bcf:field>
      <bcf:field>urlday</bcf:field>
      <bcf:field>urlendday</bcf:field>
      <bcf:field>urlendhour</bcf:field>
      <bcf:field>urlendminute</bcf:field>
      <bcf:field>urlendmonth</bcf:field>
      <bcf:field>urlendsecond</bcf:field>
      <bcf:field>urlendtimezone</bcf:field>
      <bcf:field>urlendyear</bcf:field>
      <bcf:field>urlhour</bcf:field>
      <bcf:field>urlminute</bcf:field>
      <bcf:field>urlmonth</bcf:field>
      <bcf:field>urlsecond</bcf:field>
      <bcf:field>urltimezone</bcf:field>
      <bcf:field>urlyear</bcf:field>
      <bcf:field>usera</bcf:field>
      <bcf:field>userb</bcf:field>
      <bcf:field>userc</bcf:field>
      <bcf:field>userd</bcf:field>
      <bcf:field>usere</bcf:field>
      <bcf:field>userf</bcf:field>
      <bcf:field>verba</bcf:field>
      <bcf:field>verbb</bcf:field>
      <bcf:field>verbc</bcf:field>
      <bcf:field>xdata</bcf:field>
      <bcf:field>xref</bcf:field>
      <bcf:field>year</bcf:field>
      <bcf:field>yeardivision</bcf:field>
    </bcf:entryfields>
    <bcf:entryfields>
      <bcf:entrytype>set</bcf:entrytype>
      <bcf:field>entryset</bcf:field>
    </bcf:entryfields>
    <bcf:entryfields>
      <bcf:entrytype>article</bcf:entrytype>
      <bcf:field>addendum</bcf:field>
      <bcf:field>annotator</bcf:field>
      <bcf:field>author</bcf:field>
      <bcf:field>commentator</bcf:field>
      <bcf:field>editor</bcf:field>
      <bcf:field>editora</bcf:field>
      <bcf:field>editorb</bcf:field>
      <bcf:field>editorc</bcf:field>
      <bcf:field>editortype</bcf:field>
      <bcf:field>editoratype</bcf:field>
      <bcf:field>editorbtype</bcf:field>
      <bcf:field>editorctype</bcf:field>
      <bcf:field>eid</bcf:field>
      <bcf:field>issn</bcf:field>
      <bcf:field>issue</bcf:field>
      <bcf:field>issuetitle</bcf:field>
      <bcf:field>issuesubtitle</bcf:field>
      <bcf:field>issuetitleaddon</bcf:field>
      <bcf:field>journalsubtitle</bcf:field>
      <bcf:field>journaltitle</bcf:field>
      <bcf:field>journaltitleaddon</bcf:field>
      <bcf:field>language</bcf:field>
      <bcf:field>note</bcf:field>
      <bcf:field>number</bcf:field>
      <bcf:field>origlanguage</bcf:field>
      <bcf:field>pages</bcf:field>
      <bcf:field>pubstate</bcf:field>
      <bcf:field>series</bcf:field>
      <bcf:field>subtitle</bcf:field>
      <bcf:field>title</bcf:field>
      <bcf:field>titleaddon</bcf:field>
      <bcf:field>translator</bcf:field>
      <bcf:field>version</bcf:field>
      <bcf:field>volume</bcf:field>
    </bcf:entryfields>
    <bcf:entryfields>
      <bcf:entrytype>bibnote</bcf:entrytype>
      <bcf:field>note</bcf:field>
    </bcf:entryfields>
    <bcf:entryfields>
      <bcf:entrytype>book</bcf:entrytype>
      <bcf:field>author</bcf:field>
      <bcf:field>addendum</bcf:field>
      <bcf:field>afterword</bcf:field>
      <bcf:field>annotator</bcf:field>
      <bcf:field>chapter</bcf:field>
      <bcf:field>commentator</bcf:field>
      <bcf:field>edition</bcf:field>
      <bcf:field>editor</bcf:field>
      <bcf:field>editora</bcf:field>
      <bcf:field>editorb</bcf:field>
      <bcf:field>editorc</bcf:field>
      <bcf:field>editortype</bcf:field>
      <bcf:field>editoratype</bcf:field>
      <bcf:field>editorbtype</bcf:field>
      <bcf:field>editorctype</bcf:field>
      <bcf:field>eid</bcf:field>
      <bcf:field>foreword</bcf:field>
      <bcf:field>introduction</bcf:field>
      <bcf:field>isbn</bcf:field>
      <bcf:field>language</bcf:field>
      <bcf:field>location</bcf:field>
      <bcf:field>maintitle</bcf:field>
      <bcf:field>maintitleaddon</bcf:field>
      <bcf:field>mainsubtitle</bcf:field>
      <bcf:field>note</bcf:field>
      <bcf:field>number</bcf:field>
      <bcf:field>origlanguage</bcf:field>
      <bcf:field>pages</bcf:field>
      <bcf:field>pagetotal</bcf:field>
      <bcf:field>part</bcf:field>
      <bcf:field>publisher</bcf:field>
      <bcf:field>pubstate</bcf:field>
      <bcf:field>series</bcf:field>
      <bcf:field>subtitle</bcf:field>
      <bcf:field>title</bcf:field>
      <bcf:field>titleaddon</bcf:field>
      <bcf:field>translator</bcf:field>
      <bcf:field>volume</bcf:field>
      <bcf:field>volumes</bcf:field>
    </bcf:entryfields>
    <bcf:entryfields>
      <bcf:entrytype>mvbook</bcf:entrytype>
      <bcf:field>addendum</bcf:field>
      <bcf:field>afterword</bcf:field>
      <bcf:field>annotator</bcf:field>
      <bcf:field>author</bcf:field>
      <bcf:field>commentator</bcf:field>
      <bcf:field>edition</bcf:field>
      <bcf:field>editor</bcf:field>
      <bcf:field>editora</bcf:field>
      <bcf:field>editorb</bcf:field>
      <bcf:field>editorc</bcf:field>
      <bcf:field>editortype</bcf:field>
      <bcf:field>editoratype</bcf:field>
      <bcf:field>editorbtype</bcf:field>
      <bcf:field>editorctype</bcf:field>
      <bcf:field>foreword</bcf:field>
      <bcf:field>introduction</bcf:field>
      <bcf:field>isbn</bcf:field>
      <bcf:field>language</bcf:field>
      <bcf:field>location</bcf:field>
      <bcf:field>note</bcf:field>
      <bcf:field>number</bcf:field>
      <bcf:field>origlanguage</bcf:field>
      <bcf:field>pagetotal</bcf:field>
      <bcf:field>publisher</bcf:field>
      <bcf:field>pubstate</bcf:field>
      <bcf:field>series</bcf:field>
      <bcf:field>subtitle</bcf:field>
      <bcf:field>title</bcf:field>
      <bcf:field>titleaddon</bcf:field>
      <bcf:field>translator</bcf:field>
      <bcf:field>volume</bcf:field>
      <bcf:field>volumes</bcf:field>
    </bcf:entryfields>
    <bcf:entryfields>
      <bcf:entrytype>inbook</bcf:entrytype>
      <bcf:entrytype>bookinbook</bcf:entrytype>
      <bcf:entrytype>suppbook</bcf:entrytype>
      <bcf:field>addendum</bcf:field>
      <bcf:field>afterword</bcf:field>
      <bcf:field>annotator</bcf:field>
      <bcf:field>author</bcf:field>
      <bcf:field>booktitle</bcf:field>
      <bcf:field>bookauthor</bcf:field>
      <bcf:field>booksubtitle</bcf:field>
      <bcf:field>booktitleaddon</bcf:field>
      <bcf:field>chapter</bcf:field>
      <bcf:field>commentator</bcf:field>
      <bcf:field>edition</bcf:field>
      <bcf:field>editor</bcf:field>
      <bcf:field>editora</bcf:field>
      <bcf:field>editorb</bcf:field>
      <bcf:field>editorc</bcf:field>
      <bcf:field>editortype</bcf:field>
      <bcf:field>editoratype</bcf:field>
      <bcf:field>editorbtype</bcf:field>
      <bcf:field>editorctype</bcf:field>
      <bcf:field>eid</bcf:field>
      <bcf:field>foreword</bcf:field>
      <bcf:field>introduction</bcf:field>
      <bcf:field>isbn</bcf:field>
      <bcf:field>language</bcf:field>
      <bcf:field>location</bcf:field>
      <bcf:field>mainsubtitle</bcf:field>
      <bcf:field>maintitle</bcf:field>
      <bcf:field>maintitleaddon</bcf:field>
      <bcf:field>note</bcf:field>
      <bcf:field>number</bcf:field>
      <bcf:field>origlanguage</bcf:field>
      <bcf:field>part</bcf:field>
      <bcf:field>publisher</bcf:field>
      <bcf:field>pages</bcf:field>
      <bcf:field>pubstate</bcf:field>
      <bcf:field>series</bcf:field>
      <bcf:field>subtitle</bcf:field>
      <bcf:field>title</bcf:field>
      <bcf:field>titleaddon</bcf:field>
      <bcf:field>translator</bcf:field>
      <bcf:field>volume</bcf:field>
      <bcf:field>volumes</bcf:field>
    </bcf:entryfields>
    <bcf:entryfields>
      <bcf:entrytype>booklet</bcf:entrytype>
      <bcf:field>addendum</bcf:field>
      <bcf:field>author</bcf:field>
      <bcf:field>chapter</bcf:field>
      <bcf:field>editor</bcf:field>
      <bcf:field>editortype</bcf:field>
      <bcf:field>eid</bcf:field>
      <bcf:field>howpublished</bcf:field>
      <bcf:field>language</bcf:field>
      <bcf:field>location</bcf:field>
      <bcf:field>note</bcf:field>
      <bcf:field>pages</bcf:field>
      <bcf:field>pagetotal</bcf:field>
      <bcf:field>pubstate</bcf:field>
      <bcf:field>subtitle</bcf:field>
      <bcf:field>title</bcf:field>
      <bcf:field>titleaddon</bcf:field>
      <bcf:field>type</bcf:field>
    </bcf:entryfields>
    <bcf:entryfields>
      <bcf:entrytype>collection</bcf:entrytype>
      <bcf:entrytype>reference</bcf:entrytype>
      <bcf:field>addendum</bcf:field>
      <bcf:field>afterword</bcf:field>
      <bcf:field>annotator</bcf:field>
      <bcf:field>chapter</bcf:field>
      <bcf:field>commentator</bcf:field>
      <bcf:field>edition</bcf:field>
      <bcf:field>editor</bcf:field>
      <bcf:field>editora</bcf:field>
      <bcf:field>editorb</bcf:field>
      <bcf:field>editorc</bcf:field>
      <bcf:field>editortype</bcf:field>
      <bcf:field>editoratype</bcf:field>
      <bcf:field>editorbtype</bcf:field>
      <bcf:field>editorctype</bcf:field>
      <bcf:field>eid</bcf:field>
      <bcf:field>foreword</bcf:field>
      <bcf:field>introduction</bcf:field>
      <bcf:field>isbn</bcf:field>
      <bcf:field>language</bcf:field>
      <bcf:field>location</bcf:field>
      <bcf:field>mainsubtitle</bcf:field>
      <bcf:field>maintitle</bcf:field>
      <bcf:field>maintitleaddon</bcf:field>
      <bcf:field>note</bcf:field>
      <bcf:field>number</bcf:field>
      <bcf:field>origlanguage</bcf:field>
      <bcf:field>pages</bcf:field>
      <bcf:field>pagetotal</bcf:field>
      <bcf:field>part</bcf:field>
      <bcf:field>publisher</bcf:field>
      <bcf:field>pubstate</bcf:field>
      <bcf:field>series</bcf:field>
      <bcf:field>subtitle</bcf:field>
      <bcf:field>title</bcf:field>
      <bcf:field>titleaddon</bcf:field>
      <bcf:field>translator</bcf:field>
      <bcf:field>volume</bcf:field>
      <bcf:field>volumes</bcf:field>
    </bcf:entryfields>
    <bcf:entryfields>
      <bcf:entrytype>mvcollection</bcf:entrytype>
      <bcf:entrytype>mvreference</bcf:entrytype>
      <bcf:field>addendum</bcf:field>
      <bcf:field>afterword</bcf:field>
      <bcf:field>annotator</bcf:field>
      <bcf:field>author</bcf:field>
      <bcf:field>commentator</bcf:field>
      <bcf:field>edition</bcf:field>
      <bcf:field>editor</bcf:field>
      <bcf:field>editora</bcf:field>
      <bcf:field>editorb</bcf:field>
      <bcf:field>editorc</bcf:field>
      <bcf:field>editortype</bcf:field>
      <bcf:field>editoratype</bcf:field>
      <bcf:field>editorbtype</bcf:field>
      <bcf:field>editorctype</bcf:field>
      <bcf:field>foreword</bcf:field>
      <bcf:field>introduction</bcf:field>
      <bcf:field>isbn</bcf:field>
      <bcf:field>language</bcf:field>
      <bcf:field>location</bcf:field>
      <bcf:field>note</bcf:field>
      <bcf:field>number</bcf:field>
      <bcf:field>origlanguage</bcf:field>
      <bcf:field>publisher</bcf:field>
      <bcf:field>pubstate</bcf:field>
      <bcf:field>subtitle</bcf:field>
      <bcf:field>title</bcf:field>
      <bcf:field>titleaddon</bcf:field>
      <bcf:field>translator</bcf:field>
      <bcf:field>volume</bcf:field>
      <bcf:field>volumes</bcf:field>
    </bcf:entryfields>
    <bcf:entryfields>
      <bcf:entrytype>incollection</bcf:entrytype>
      <bcf:entrytype>suppcollection</bcf:entrytype>
      <bcf:entrytype>inreference</bcf:entrytype>
      <bcf:field>addendum</bcf:field>
      <bcf:field>afterword</bcf:field>
      <bcf:field>annotator</bcf:field>
      <bcf:field>author</bcf:field>
      <bcf:field>booksubtitle</bcf:field>
      <bcf:field>booktitle</bcf:field>
      <bcf:field>booktitleaddon</bcf:field>
      <bcf:field>chapter</bcf:field>
      <bcf:field>commentator</bcf:field>
      <bcf:field>edition</bcf:field>
      <bcf:field>editor</bcf:field>
      <bcf:field>editora</bcf:field>
      <bcf:field>editorb</bcf:field>
      <bcf:field>editorc</bcf:field>
      <bcf:field>editortype</bcf:field>
      <bcf:field>editoratype</bcf:field>
      <bcf:field>editorbtype</bcf:field>
      <bcf:field>editorctype</bcf:field>
      <bcf:field>eid</bcf:field>
      <bcf:field>foreword</bcf:field>
      <bcf:field>introduction</bcf:field>
      <bcf:field>isbn</bcf:field>
      <bcf:field>language</bcf:field>
      <bcf:field>location</bcf:field>
      <bcf:field>mainsubtitle</bcf:field>
      <bcf:field>maintitle</bcf:field>
      <bcf:field>maintitleaddon</bcf:field>
      <bcf:field>note</bcf:field>
      <bcf:field>number</bcf:field>
      <bcf:field>origlanguage</bcf:field>
      <bcf:field>pages</bcf:field>
      <bcf:field>part</bcf:field>
      <bcf:field>publisher</bcf:field>
      <bcf:field>pubstate</bcf:field>
      <bcf:field>series</bcf:field>
      <bcf:field>subtitle</bcf:field>
      <bcf:field>title</bcf:field>
      <bcf:field>titleaddon</bcf:field>
      <bcf:field>translator</bcf:field>
      <bcf:field>volume</bcf:field>
      <bcf:field>volumes</bcf:field>
    </bcf:entryfields>
    <bcf:entryfields>
      <bcf:entrytype>dataset</bcf:entrytype>
      <bcf:field>addendum</bcf:field>
      <bcf:field>author</bcf:field>
      <bcf:field>edition</bcf:field>
      <bcf:field>editor</bcf:field>
      <bcf:field>editortype</bcf:field>
      <bcf:field>language</bcf:field>
      <bcf:field>location</bcf:field>
      <bcf:field>note</bcf:field>
      <bcf:field>number</bcf:field>
      <bcf:field>organization</bcf:field>
      <bcf:field>publisher</bcf:field>
      <bcf:field>pubstate</bcf:field>
      <bcf:field>series</bcf:field>
      <bcf:field>subtitle</bcf:field>
      <bcf:field>title</bcf:field>
      <bcf:field>titleaddon</bcf:field>
      <bcf:field>type</bcf:field>
      <bcf:field>version</bcf:field>
    </bcf:entryfields>
    <bcf:entryfields>
      <bcf:entrytype>manual</bcf:entrytype>
      <bcf:field>addendum</bcf:field>
      <bcf:field>author</bcf:field>
      <bcf:field>chapter</bcf:field>
      <bcf:field>edition</bcf:field>
      <bcf:field>editor</bcf:field>
      <bcf:field>editortype</bcf:field>
      <bcf:field>eid</bcf:field>
      <bcf:field>isbn</bcf:field>
      <bcf:field>language</bcf:field>
      <bcf:field>location</bcf:field>
      <bcf:field>note</bcf:field>
      <bcf:field>number</bcf:field>
      <bcf:field>organization</bcf:field>
      <bcf:field>pages</bcf:field>
      <bcf:field>pagetotal</bcf:field>
      <bcf:field>publisher</bcf:field>
      <bcf:field>pubstate</bcf:field>
      <bcf:field>series</bcf:field>
      <bcf:field>subtitle</bcf:field>
      <bcf:field>title</bcf:field>
      <bcf:field>titleaddon</bcf:field>
      <bcf:field>type</bcf:field>
      <bcf:field>version</bcf:field>
    </bcf:entryfields>
    <bcf:entryfields>
      <bcf:entrytype>misc</bcf:entrytype>
      <bcf:entrytype>software</bcf:entrytype>
      <bcf:field>addendum</bcf:field>
      <bcf:field>author</bcf:field>
      <bcf:field>editor</bcf:field>
      <bcf:field>editortype</bcf:field>
      <bcf:field>howpublished</bcf:field>
      <bcf:field>language</bcf:field>
      <bcf:field>location</bcf:field>
      <bcf:field>note</bcf:field>
      <bcf:field>organization</bcf:field>
      <bcf:field>pubstate</bcf:field>
      <bcf:field>subtitle</bcf:field>
      <bcf:field>title</bcf:field>
      <bcf:field>titleaddon</bcf:field>
      <bcf:field>type</bcf:field>
      <bcf:field>version</bcf:field>
    </bcf:entryfields>
    <bcf:entryfields>
      <bcf:entrytype>online</bcf:entrytype>
      <bcf:field>addendum</bcf:field>
      <bcf:field>author</bcf:field>
      <bcf:field>editor</bcf:field>
      <bcf:field>editortype</bcf:field>
      <bcf:field>language</bcf:field>
      <bcf:field>note</bcf:field>
      <bcf:field>organization</bcf:field>
      <bcf:field>pubstate</bcf:field>
      <bcf:field>subtitle</bcf:field>
      <bcf:field>title</bcf:field>
      <bcf:field>titleaddon</bcf:field>
      <bcf:field>version</bcf:field>
    </bcf:entryfields>
    <bcf:entryfields>
      <bcf:entrytype>patent</bcf:entrytype>
      <bcf:field>addendum</bcf:field>
      <bcf:field>author</bcf:field>
      <bcf:field>holder</bcf:field>
      <bcf:field>location</bcf:field>
      <bcf:field>note</bcf:field>
      <bcf:field>number</bcf:field>
      <bcf:field>pubstate</bcf:field>
      <bcf:field>subtitle</bcf:field>
      <bcf:field>title</bcf:field>
      <bcf:field>titleaddon</bcf:field>
      <bcf:field>type</bcf:field>
      <bcf:field>version</bcf:field>
    </bcf:entryfields>
    <bcf:entryfields>
      <bcf:entrytype>periodical</bcf:entrytype>
      <bcf:field>addendum</bcf:field>
      <bcf:field>editor</bcf:field>
      <bcf:field>editora</bcf:field>
      <bcf:field>editorb</bcf:field>
      <bcf:field>editorc</bcf:field>
      <bcf:field>editortype</bcf:field>
      <bcf:field>editoratype</bcf:field>
      <bcf:field>editorbtype</bcf:field>
      <bcf:field>editorctype</bcf:field>
      <bcf:field>issn</bcf:field>
      <bcf:field>issue</bcf:field>
      <bcf:field>issuesubtitle</bcf:field>
      <bcf:field>issuetitle</bcf:field>
      <bcf:field>issuetitleaddon</bcf:field>
      <bcf:field>language</bcf:field>
      <bcf:field>note</bcf:field>
      <bcf:field>number</bcf:field>
      <bcf:field>pubstate</bcf:field>
      <bcf:field>series</bcf:field>
      <bcf:field>subtitle</bcf:field>
      <bcf:field>title</bcf:field>
      <bcf:field>titleaddon</bcf:field>
      <bcf:field>volume</bcf:field>
      <bcf:field>yeardivision</bcf:field>
    </bcf:entryfields>
    <bcf:entryfields>
      <bcf:entrytype>mvproceedings</bcf:entrytype>
      <bcf:field>addendum</bcf:field>
      <bcf:field>editor</bcf:field>
      <bcf:field>editortype</bcf:field>
      <bcf:field>eventday</bcf:field>
      <bcf:field>eventendday</bcf:field>
      <bcf:field>eventendhour</bcf:field>
      <bcf:field>eventendminute</bcf:field>
      <bcf:field>eventendmonth</bcf:field>
      <bcf:field>eventendsecond</bcf:field>
      <bcf:field>eventendtimezone</bcf:field>
      <bcf:field>eventendyear</bcf:field>
      <bcf:field>eventendyeardivision</bcf:field>
      <bcf:field>eventhour</bcf:field>
      <bcf:field>eventminute</bcf:field>
      <bcf:field>eventmonth</bcf:field>
      <bcf:field>eventsecond</bcf:field>
      <bcf:field>eventtimezone</bcf:field>
      <bcf:field>eventyear</bcf:field>
      <bcf:field>eventyeardivision</bcf:field>
      <bcf:field>eventtitle</bcf:field>
      <bcf:field>eventtitleaddon</bcf:field>
      <bcf:field>isbn</bcf:field>
      <bcf:field>language</bcf:field>
      <bcf:field>location</bcf:field>
      <bcf:field>note</bcf:field>
      <bcf:field>number</bcf:field>
      <bcf:field>organization</bcf:field>
      <bcf:field>pagetotal</bcf:field>
      <bcf:field>publisher</bcf:field>
      <bcf:field>pubstate</bcf:field>
      <bcf:field>series</bcf:field>
      <bcf:field>subtitle</bcf:field>
      <bcf:field>title</bcf:field>
      <bcf:field>titleaddon</bcf:field>
      <bcf:field>venue</bcf:field>
      <bcf:field>volumes</bcf:field>
    </bcf:entryfields>
    <bcf:entryfields>
      <bcf:entrytype>proceedings</bcf:entrytype>
      <bcf:field>addendum</bcf:field>
      <bcf:field>chapter</bcf:field>
      <bcf:field>editor</bcf:field>
      <bcf:field>editortype</bcf:field>
      <bcf:field>eid</bcf:field>
      <bcf:field>eventday</bcf:field>
      <bcf:field>eventendday</bcf:field>
      <bcf:field>eventendhour</bcf:field>
      <bcf:field>eventendminute</bcf:field>
      <bcf:field>eventendmonth</bcf:field>
      <bcf:field>eventendsecond</bcf:field>
      <bcf:field>eventendtimezone</bcf:field>
      <bcf:field>eventendyear</bcf:field>
      <bcf:field>eventendyeardivision</bcf:field>
      <bcf:field>eventhour</bcf:field>
      <bcf:field>eventminute</bcf:field>
      <bcf:field>eventmonth</bcf:field>
      <bcf:field>eventsecond</bcf:field>
      <bcf:field>eventtimezone</bcf:field>
      <bcf:field>eventyear</bcf:field>
      <bcf:field>eventyeardivision</bcf:field>
      <bcf:field>eventtitle</bcf:field>
      <bcf:field>eventtitleaddon</bcf:field>
      <bcf:field>isbn</bcf:field>
      <bcf:field>language</bcf:field>
      <bcf:field>location</bcf:field>
      <bcf:field>mainsubtitle</bcf:field>
      <bcf:field>maintitle</bcf:field>
      <bcf:field>maintitleaddon</bcf:field>
      <bcf:field>note</bcf:field>
      <bcf:field>number</bcf:field>
      <bcf:field>organization</bcf:field>
      <bcf:field>pages</bcf:field>
      <bcf:field>pagetotal</bcf:field>
      <bcf:field>part</bcf:field>
      <bcf:field>publisher</bcf:field>
      <bcf:field>pubstate</bcf:field>
      <bcf:field>series</bcf:field>
      <bcf:field>subtitle</bcf:field>
      <bcf:field>title</bcf:field>
      <bcf:field>titleaddon</bcf:field>
      <bcf:field>venue</bcf:field>
      <bcf:field>volume</bcf:field>
      <bcf:field>volumes</bcf:field>
    </bcf:entryfields>
    <bcf:entryfields>
      <bcf:entrytype>inproceedings</bcf:entrytype>
      <bcf:field>addendum</bcf:field>
      <bcf:field>author</bcf:field>
      <bcf:field>booksubtitle</bcf:field>
      <bcf:field>booktitle</bcf:field>
      <bcf:field>booktitleaddon</bcf:field>
      <bcf:field>chapter</bcf:field>
      <bcf:field>editor</bcf:field>
      <bcf:field>editortype</bcf:field>
      <bcf:field>eid</bcf:field>
      <bcf:field>eventday</bcf:field>
      <bcf:field>eventendday</bcf:field>
      <bcf:field>eventendhour</bcf:field>
      <bcf:field>eventendminute</bcf:field>
      <bcf:field>eventendmonth</bcf:field>
      <bcf:field>eventendsecond</bcf:field>
      <bcf:field>eventendtimezone</bcf:field>
      <bcf:field>eventendyear</bcf:field>
      <bcf:field>eventendyeardivision</bcf:field>
      <bcf:field>eventhour</bcf:field>
      <bcf:field>eventminute</bcf:field>
      <bcf:field>eventmonth</bcf:field>
      <bcf:field>eventsecond</bcf:field>
      <bcf:field>eventtimezone</bcf:field>
      <bcf:field>eventyear</bcf:field>
      <bcf:field>eventyeardivision</bcf:field>
      <bcf:field>eventtitle</bcf:field>
      <bcf:field>eventtitleaddon</bcf:field>
      <bcf:field>isbn</bcf:field>
      <bcf:field>language</bcf:field>
      <bcf:field>location</bcf:field>
      <bcf:field>mainsubtitle</bcf:field>
      <bcf:field>maintitle</bcf:field>
      <bcf:field>maintitleaddon</bcf:field>
      <bcf:field>note</bcf:field>
      <bcf:field>number</bcf:field>
      <bcf:field>organization</bcf:field>
      <bcf:field>pages</bcf:field>
      <bcf:field>part</bcf:field>
      <bcf:field>publisher</bcf:field>
      <bcf:field>pubstate</bcf:field>
      <bcf:field>series</bcf:field>
      <bcf:field>subtitle</bcf:field>
      <bcf:field>title</bcf:field>
      <bcf:field>titleaddon</bcf:field>
      <bcf:field>venue</bcf:field>
      <bcf:field>volume</bcf:field>
      <bcf:field>volumes</bcf:field>
    </bcf:entryfields>
    <bcf:entryfields>
      <bcf:entrytype>report</bcf:entrytype>
      <bcf:field>addendum</bcf:field>
      <bcf:field>author</bcf:field>
      <bcf:field>chapter</bcf:field>
      <bcf:field>eid</bcf:field>
      <bcf:field>institution</bcf:field>
      <bcf:field>isrn</bcf:field>
      <bcf:field>language</bcf:field>
      <bcf:field>location</bcf:field>
      <bcf:field>note</bcf:field>
      <bcf:field>number</bcf:field>
      <bcf:field>pages</bcf:field>
      <bcf:field>pagetotal</bcf:field>
      <bcf:field>pubstate</bcf:field>
      <bcf:field>subtitle</bcf:field>
      <bcf:field>title</bcf:field>
      <bcf:field>titleaddon</bcf:field>
      <bcf:field>type</bcf:field>
      <bcf:field>version</bcf:field>
    </bcf:entryfields>
    <bcf:entryfields>
      <bcf:entrytype>thesis</bcf:entrytype>
      <bcf:field>addendum</bcf:field>
      <bcf:field>author</bcf:field>
      <bcf:field>chapter</bcf:field>
      <bcf:field>eid</bcf:field>
      <bcf:field>institution</bcf:field>
      <bcf:field>language</bcf:field>
      <bcf:field>location</bcf:field>
      <bcf:field>note</bcf:field>
      <bcf:field>pages</bcf:field>
      <bcf:field>pagetotal</bcf:field>
      <bcf:field>pubstate</bcf:field>
      <bcf:field>subtitle</bcf:field>
      <bcf:field>title</bcf:field>
      <bcf:field>titleaddon</bcf:field>
      <bcf:field>type</bcf:field>
    </bcf:entryfields>
    <bcf:entryfields>
      <bcf:entrytype>unpublished</bcf:entrytype>
      <bcf:field>addendum</bcf:field>
      <bcf:field>author</bcf:field>
      <bcf:field>eventday</bcf:field>
      <bcf:field>eventendday</bcf:field>
      <bcf:field>eventendhour</bcf:field>
      <bcf:field>eventendminute</bcf:field>
      <bcf:field>eventendmonth</bcf:field>
      <bcf:field>eventendsecond</bcf:field>
      <bcf:field>eventendtimezone</bcf:field>
      <bcf:field>eventendyear</bcf:field>
      <bcf:field>eventendyeardivision</bcf:field>
      <bcf:field>eventhour</bcf:field>
      <bcf:field>eventminute</bcf:field>
      <bcf:field>eventmonth</bcf:field>
      <bcf:field>eventsecond</bcf:field>
      <bcf:field>eventtimezone</bcf:field>
      <bcf:field>eventyear</bcf:field>
      <bcf:field>eventyeardivision</bcf:field>
      <bcf:field>eventtitle</bcf:field>
      <bcf:field>eventtitleaddon</bcf:field>
      <bcf:field>howpublished</bcf:field>
      <bcf:field>language</bcf:field>
      <bcf:field>location</bcf:field>
      <bcf:field>note</bcf:field>
      <bcf:field>pubstate</bcf:field>
      <bcf:field>subtitle</bcf:field>
      <bcf:field>title</bcf:field>
      <bcf:field>titleaddon</bcf:field>
      <bcf:field>type</bcf:field>
      <bcf:field>venue</bcf:field>
    </bcf:entryfields>
    <bcf:multiscriptfields>
      <bcf:field>abstract</bcf:field>
      <bcf:field>addendum</bcf:field>
      <bcf:field>afterword</bcf:field>
      <bcf:field>annotator</bcf:field>
      <bcf:field>author</bcf:field>
      <bcf:field>bookauthor</bcf:field>
      <bcf:field>booksubtitle</bcf:field>
      <bcf:field>booktitle</bcf:field>
      <bcf:field>booktitleaddon</bcf:field>
      <bcf:field>chapter</bcf:field>
      <bcf:field>commentator</bcf:field>
      <bcf:field>editor</bcf:field>
      <bcf:field>editora</bcf:field>
      <bcf:field>editorb</bcf:field>
      <bcf:field>editorc</bcf:field>
      <bcf:field>foreword</bcf:field>
      <bcf:field>holder</bcf:field>
      <bcf:field>institution</bcf:field>
      <bcf:field>introduction</bcf:field>
      <bcf:field>issuesubtitle</bcf:field>
      <bcf:field>issuetitle</bcf:field>
      <bcf:field>issuetitleaddon</bcf:field>
      <bcf:field>journalsubtitle</bcf:field>
      <bcf:field>journaltitle</bcf:field>
      <bcf:field>journaltitleaddon</bcf:field>
      <bcf:field>location</bcf:field>
      <bcf:field>mainsubtitle</bcf:field>
      <bcf:field>maintitle</bcf:field>
      <bcf:field>maintitleaddon</bcf:field>
      <bcf:field>nameaddon</bcf:field>
      <bcf:field>note</bcf:field>
      <bcf:field>organization</bcf:field>
      <bcf:field>origlanguage</bcf:field>
      <bcf:field>origlocation</bcf:field>
      <bcf:field>origpublisher</bcf:field>
      <bcf:field>origtitle</bcf:field>
      <bcf:field>part</bcf:field>
      <bcf:field>publisher</bcf:field>
      <bcf:field>relatedstring</bcf:field>
      <bcf:field>series</bcf:field>
      <bcf:field>shortauthor</bcf:field>
      <bcf:field>shorteditor</bcf:field>
      <bcf:field>shorthand</bcf:field>
      <bcf:field>shortjournal</bcf:field>
      <bcf:field>shortseries</bcf:field>
      <bcf:field>shorttitle</bcf:field>
      <bcf:field>sortname</bcf:field>
      <bcf:field>sortshorthand</bcf:field>
      <bcf:field>sorttitle</bcf:field>
      <bcf:field>subtitle</bcf:field>
      <bcf:field>title</bcf:field>
      <bcf:field>titleaddon</bcf:field>
      <bcf:field>translator</bcf:field>
      <bcf:field>venue</bcf:field>
    </bcf:multiscriptfields>
    <bcf:constraints>
      <bcf:entrytype>article</bcf:entrytype>
      <bcf:entrytype>book</bcf:entrytype>
      <bcf:entrytype>inbook</bcf:entrytype>
      <bcf:entrytype>bookinbook</bcf:entrytype>
      <bcf:entrytype>suppbook</bcf:entrytype>
      <bcf:entrytype>booklet</bcf:entrytype>
      <bcf:entrytype>collection</bcf:entrytype>
      <bcf:entrytype>incollection</bcf:entrytype>
      <bcf:entrytype>suppcollection</bcf:entrytype>
      <bcf:entrytype>manual</bcf:entrytype>
      <bcf:entrytype>misc</bcf:entrytype>
      <bcf:entrytype>mvbook</bcf:entrytype>
      <bcf:entrytype>mvcollection</bcf:entrytype>
      <bcf:entrytype>online</bcf:entrytype>
      <bcf:entrytype>patent</bcf:entrytype>
      <bcf:entrytype>periodical</bcf:entrytype>
      <bcf:entrytype>suppperiodical</bcf:entrytype>
      <bcf:entrytype>proceedings</bcf:entrytype>
      <bcf:entrytype>inproceedings</bcf:entrytype>
      <bcf:entrytype>reference</bcf:entrytype>
      <bcf:entrytype>inreference</bcf:entrytype>
      <bcf:entrytype>report</bcf:entrytype>
      <bcf:entrytype>set</bcf:entrytype>
      <bcf:entrytype>thesis</bcf:entrytype>
      <bcf:entrytype>unpublished</bcf:entrytype>
      <bcf:constraint type="mandatory">
        <bcf:fieldxor>
          <bcf:field>date</bcf:field>
          <bcf:field>year</bcf:field>
        </bcf:fieldxor>
      </bcf:constraint>
    </bcf:constraints>
    <bcf:constraints>
      <bcf:entrytype>set</bcf:entrytype>
      <bcf:constraint type="mandatory">
        <bcf:field>entryset</bcf:field>
      </bcf:constraint>
    </bcf:constraints>
    <bcf:constraints>
      <bcf:entrytype>article</bcf:entrytype>
      <bcf:constraint type="mandatory">
        <bcf:field>author</bcf:field>
        <bcf:field>journaltitle</bcf:field>
        <bcf:field>title</bcf:field>
      </bcf:constraint>
    </bcf:constraints>
    <bcf:constraints>
      <bcf:entrytype>book</bcf:entrytype>
      <bcf:entrytype>mvbook</bcf:entrytype>
      <bcf:constraint type="mandatory">
        <bcf:field>author</bcf:field>
        <bcf:field>title</bcf:field>
      </bcf:constraint>
    </bcf:constraints>
    <bcf:constraints>
      <bcf:entrytype>inbook</bcf:entrytype>
      <bcf:entrytype>bookinbook</bcf:entrytype>
      <bcf:entrytype>suppbook</bcf:entrytype>
      <bcf:constraint type="mandatory">
        <bcf:field>author</bcf:field>
        <bcf:field>title</bcf:field>
        <bcf:field>booktitle</bcf:field>
      </bcf:constraint>
    </bcf:constraints>
    <bcf:constraints>
      <bcf:entrytype>booklet</bcf:entrytype>
      <bcf:constraint type="mandatory">
        <bcf:fieldor>
          <bcf:field>author</bcf:field>
          <bcf:field>editor</bcf:field>
        </bcf:fieldor>
        <bcf:field>title</bcf:field>
      </bcf:constraint>
    </bcf:constraints>
    <bcf:constraints>
      <bcf:entrytype>collection</bcf:entrytype>
      <bcf:entrytype>reference</bcf:entrytype>
      <bcf:entrytype>mvcollection</bcf:entrytype>
      <bcf:entrytype>mvreference</bcf:entrytype>
      <bcf:constraint type="mandatory">
        <bcf:field>editor</bcf:field>
        <bcf:field>title</bcf:field>
      </bcf:constraint>
    </bcf:constraints>
    <bcf:constraints>
      <bcf:entrytype>incollection</bcf:entrytype>
      <bcf:entrytype>suppcollection</bcf:entrytype>
      <bcf:entrytype>inreference</bcf:entrytype>
      <bcf:constraint type="mandatory">
        <bcf:field>author</bcf:field>
        <bcf:field>editor</bcf:field>
        <bcf:field>title</bcf:field>
        <bcf:field>booktitle</bcf:field>
      </bcf:constraint>
    </bcf:constraints>
    <bcf:constraints>
      <bcf:entrytype>dataset</bcf:entrytype>
      <bcf:constraint type="mandatory">
        <bcf:field>title</bcf:field>
      </bcf:constraint>
    </bcf:constraints>
    <bcf:constraints>
      <bcf:entrytype>manual</bcf:entrytype>
      <bcf:constraint type="mandatory">
        <bcf:field>title</bcf:field>
      </bcf:constraint>
    </bcf:constraints>
    <bcf:constraints>
      <bcf:entrytype>misc</bcf:entrytype>
      <bcf:entrytype>software</bcf:entrytype>
      <bcf:constraint type="mandatory">
        <bcf:field>title</bcf:field>
      </bcf:constraint>
    </bcf:constraints>
    <bcf:constraints>
      <bcf:entrytype>online</bcf:entrytype>
      <bcf:constraint type="mandatory">
        <bcf:field>title</bcf:field>
        <bcf:fieldor>
          <bcf:field>url</bcf:field>
          <bcf:field>doi</bcf:field>
          <bcf:field>eprint</bcf:field>
        </bcf:fieldor>
      </bcf:constraint>
    </bcf:constraints>
    <bcf:constraints>
      <bcf:entrytype>patent</bcf:entrytype>
      <bcf:constraint type="mandatory">
        <bcf:field>author</bcf:field>
        <bcf:field>title</bcf:field>
        <bcf:field>number</bcf:field>
      </bcf:constraint>
    </bcf:constraints>
    <bcf:constraints>
      <bcf:entrytype>periodical</bcf:entrytype>
      <bcf:constraint type="mandatory">
        <bcf:field>editor</bcf:field>
        <bcf:field>title</bcf:field>
      </bcf:constraint>
    </bcf:constraints>
    <bcf:constraints>
      <bcf:entrytype>proceedings</bcf:entrytype>
      <bcf:entrytype>mvproceedings</bcf:entrytype>
      <bcf:constraint type="mandatory">
        <bcf:field>title</bcf:field>
      </bcf:constraint>
    </bcf:constraints>
    <bcf:constraints>
      <bcf:entrytype>inproceedings</bcf:entrytype>
      <bcf:constraint type="mandatory">
        <bcf:field>author</bcf:field>
        <bcf:field>title</bcf:field>
        <bcf:field>booktitle</bcf:field>
      </bcf:constraint>
    </bcf:constraints>
    <bcf:constraints>
      <bcf:entrytype>report</bcf:entrytype>
      <bcf:constraint type="mandatory">
        <bcf:field>author</bcf:field>
        <bcf:field>title</bcf:field>
        <bcf:field>type</bcf:field>
        <bcf:field>institution</bcf:field>
      </bcf:constraint>
    </bcf:constraints>
    <bcf:constraints>
      <bcf:entrytype>thesis</bcf:entrytype>
      <bcf:constraint type="mandatory">
        <bcf:field>author</bcf:field>
        <bcf:field>title</bcf:field>
        <bcf:field>type</bcf:field>
        <bcf:field>institution</bcf:field>
      </bcf:constraint>
    </bcf:constraints>
    <bcf:constraints>
      <bcf:entrytype>unpublished</bcf:entrytype>
      <bcf:constraint type="mandatory">
        <bcf:field>author</bcf:field>
        <bcf:field>title</bcf:field>
      </bcf:constraint>
    </bcf:constraints>
    <bcf:constraints>
      <bcf:constraint type="data" datatype="isbn">
        <bcf:field>isbn</bcf:field>
      </bcf:constraint>
      <bcf:constraint type="data" datatype="issn">
        <bcf:field>issn</bcf:field>
      </bcf:constraint>
      <bcf:constraint type="data" datatype="ismn">
        <bcf:field>ismn</bcf:field>
      </bcf:constraint>
      <bcf:constraint type="data" datatype="pattern" pattern="(?:sf|sm|sn|pf|pm|pn|pp)">
        <bcf:field>gender</bcf:field>
      </bcf:constraint>
    </bcf:constraints>
  </bcf:datamodel>
  <!-- CITATION DATA -->
  <!-- SECTION 0 -->
  <bcf:bibdata section="0">
    <bcf:datasource type="file" datatype="bibtex" glob="false">myexample.bib</bcf:datasource>
  </bcf:bibdata>
  <bcf:section number="0">
  </bcf:section>
  <!-- SECTION 1 -->
  <bcf:bibdata section="1">
    <bcf:datasource type="file" datatype="bibtex" glob="false">myexample.bib</bcf:datasource>
  </bcf:bibdata>
  <bcf:section number="1">
    <bcf:citekey order="1">CALLAN1976334</bcf:citekey>
    <bcf:citekey order="2">BRINK1983323</bcf:citekey>
    <bcf:citekey order="3">Maldacena:1997re</bcf:citekey>
    <bcf:citekey order="4">Beisert:2003yb</bcf:citekey>
    <bcf:citekey order="5">Alday:2009aq</bcf:citekey>
    <bcf:citekey order="6">Nekrasov:2009rc</bcf:citekey>
    <bcf:citekey order="7">Aminov:2020yma</bcf:citekey>
    <bcf:citekey order="8">Seiberg:1994aj</bcf:citekey>
    <bcf:citekey order="9">Beem:2013sza</bcf:citekey>
    <bcf:citekey order="10">Cordova:2017mhb</bcf:citekey>
    <bcf:citekey order="10">Bianchi:2019sxz</bcf:citekey>
    <bcf:citekey order="11">Moore:1991ks</bcf:citekey>
    <bcf:citekey order="11">PhysRevB.58.15717</bcf:citekey>
    <bcf:citekey order="12">Mathur:1988na</bcf:citekey>
    <bcf:citekey order="12">Eguchi:1987qd</bcf:citekey>
    <bcf:citekey order="13">Serre:1993</bcf:citekey>
    <bcf:citekey order="14">Chandra:2018pjq</bcf:citekey>
    <bcf:citekey order="14">Mukhi:2020gnj</bcf:citekey>
    <bcf:citekey order="14">Bae:2020xzl</bcf:citekey>
    <bcf:citekey order="14">Bae:2021mej</bcf:citekey>
    <bcf:citekey order="14">Mukhi:2022bte</bcf:citekey>
    <bcf:citekey order="14">Das:2021uvd</bcf:citekey>
    <bcf:citekey order="15">1952PhRv...87..410L</bcf:citekey>
    <bcf:citekey order="15">PhysRevLett.40.1610</bcf:citekey>
    <bcf:citekey order="16">PhysRevLett.42.1698</bcf:citekey>
    <bcf:citekey order="17">PhysRevResearch.2.033069</bcf:citekey>
    <bcf:citekey order="17">Rottoli:2024tvr</bcf:citekey>
    <bcf:citekey order="18">Gaiotto:2009we</bcf:citekey>
    <bcf:citekey order="19">Argyres:1995jj</bcf:citekey>
    <bcf:citekey order="19">Xie:2012hs</bcf:citekey>
    <bcf:citekey order="20">Witten:2007td</bcf:citekey>
    <bcf:citekey order="21">Gaiotto:2009hg</bcf:citekey>
    <bcf:citekey order="22">Gaiotto:2009hg</bcf:citekey>
    <bcf:citekey order="22">Bershadsky:1995vm</bcf:citekey>
    <bcf:citekey order="22">Seiberg:1996nz</bcf:citekey>
    <bcf:citekey order="23">Hitchin:1986vp</bcf:citekey>
    <bcf:citekey order="23">Simpson:1988</bcf:citekey>
    <bcf:citekey order="23">Donaldson:1987</bcf:citekey>
    <bcf:citekey order="23">Gaiotto:2009hg</bcf:citekey>
    <bcf:citekey order="24">Gaiotto:2009hg</bcf:citekey>
    <bcf:citekey order="25">Gadde:2011uv</bcf:citekey>
    <bcf:citekey order="26">Benini:2011nc</bcf:citekey>
    <bcf:citekey order="26">Xie:2012hs</bcf:citekey>
    <bcf:citekey order="27">Beem:2013sza</bcf:citekey>
    <bcf:citekey order="28">Beem:2017ooy</bcf:citekey>
    <bcf:citekey order="29">Kaidi:2022sng</bcf:citekey>
    <bcf:citekey order="30">zhu1996modular</bcf:citekey>
    <bcf:citekey order="31">Beem:2017ooy</bcf:citekey>
    <bcf:citekey order="32">2017arXiv170906142F</bcf:citekey>
    <bcf:citekey order="32">Fredrickson:2017yka</bcf:citekey>
    <bcf:citekey order="32">Shan:2023xtw</bcf:citekey>
    <bcf:citekey order="32">Shan:2024yas</bcf:citekey>
    <bcf:citekey order="33">Xie:2016evu</bcf:citekey>
    <bcf:citekey order="33">Xie:2019yds</bcf:citekey>
    <bcf:citekey order="33">Xie:2019zlb</bcf:citekey>
    <bcf:citekey order="34">Cordova:2015nma</bcf:citekey>
    <bcf:citekey order="35">Hitchin:1986vp</bcf:citekey>
    <bcf:citekey order="36">Fredrickson:2021cuf</bcf:citekey>
    <bcf:citekey order="36">Shan:2023xtw</bcf:citekey>
    <bcf:citekey order="36">Shan:2024yas</bcf:citekey>
    <bcf:citekey order="37">Kac:1988</bcf:citekey>
    <bcf:citekey order="38">Gukov:2022gei</bcf:citekey>
    <bcf:citekey order="39">CALLAN1976334</bcf:citekey>
    <bcf:citekey order="40">BRINK1983323</bcf:citekey>
    <bcf:citekey order="41">Maldacena:1997re</bcf:citekey>
    <bcf:citekey order="42">Beisert:2003yb</bcf:citekey>
    <bcf:citekey order="43">Alday:2009aq</bcf:citekey>
    <bcf:citekey order="44">Nekrasov:2009rc</bcf:citekey>
    <bcf:citekey order="45">Aminov:2020yma</bcf:citekey>
    <bcf:citekey order="46">Seiberg:1994aj</bcf:citekey>
    <bcf:citekey order="47">Beem:2013sza</bcf:citekey>
    <bcf:citekey order="48">Cordova:2017mhb</bcf:citekey>
    <bcf:citekey order="49">Bianchi:2019sxz</bcf:citekey>
    <bcf:citekey order="50">Moore:1991ks</bcf:citekey>
    <bcf:citekey order="51">PhysRevB.58.15717</bcf:citekey>
    <bcf:citekey order="52">Mathur:1988na</bcf:citekey>
    <bcf:citekey order="53">Eguchi:1987qd</bcf:citekey>
    <bcf:citekey order="54">Serre:1993</bcf:citekey>
    <bcf:citekey order="55">Chandra:2018pjq</bcf:citekey>
    <bcf:citekey order="56">Mukhi:2020gnj</bcf:citekey>
    <bcf:citekey order="57">Bae:2020xzl</bcf:citekey>
    <bcf:citekey order="58">Bae:2021mej</bcf:citekey>
    <bcf:citekey order="59">Mukhi:2022bte</bcf:citekey>
    <bcf:citekey order="60">Das:2021uvd</bcf:citekey>
    <bcf:citekey order="61">1952PhRv...87..410L</bcf:citekey>
    <bcf:citekey order="62">PhysRevLett.40.1610</bcf:citekey>
    <bcf:citekey order="63">PhysRevLett.42.1698</bcf:citekey>
    <bcf:citekey order="64">PhysRevResearch.2.033069</bcf:citekey>
    <bcf:citekey order="65">Rottoli:2024tvr</bcf:citekey>
    <bcf:citekey order="66">Gaiotto:2009we</bcf:citekey>
    <bcf:citekey order="67">Argyres:1995jj</bcf:citekey>
    <bcf:citekey order="68">Xie:2012hs</bcf:citekey>
    <bcf:citekey order="69">Witten:2007td</bcf:citekey>
    <bcf:citekey order="70">Gaiotto:2009hg</bcf:citekey>
    <bcf:citekey order="71">Bershadsky:1995vm</bcf:citekey>
    <bcf:citekey order="72">Seiberg:1996nz</bcf:citekey>
    <bcf:citekey order="73">Hitchin:1986vp</bcf:citekey>
    <bcf:citekey order="74">Simpson:1988</bcf:citekey>
    <bcf:citekey order="75">Donaldson:1987</bcf:citekey>
    <bcf:citekey order="76">Gadde:2011uv</bcf:citekey>
    <bcf:citekey order="77">Benini:2011nc</bcf:citekey>
    <bcf:citekey order="78">Beem:2017ooy</bcf:citekey>
    <bcf:citekey order="79">Kaidi:2022sng</bcf:citekey>
    <bcf:citekey order="80">zhu1996modular</bcf:citekey>
    <bcf:citekey order="81">2017arXiv170906142F</bcf:citekey>
    <bcf:citekey order="82">Fredrickson:2017yka</bcf:citekey>
    <bcf:citekey order="83">Shan:2023xtw</bcf:citekey>
    <bcf:citekey order="84">Shan:2024yas</bcf:citekey>
    <bcf:citekey order="85">Xie:2016evu</bcf:citekey>
    <bcf:citekey order="86">Xie:2019yds</bcf:citekey>
    <bcf:citekey order="87">Xie:2019zlb</bcf:citekey>
    <bcf:citekey order="88">Cordova:2015nma</bcf:citekey>
    <bcf:citekey order="89">Fredrickson:2021cuf</bcf:citekey>
    <bcf:citekey order="90">Kac:1988</bcf:citekey>
    <bcf:citekey order="91">Gukov:2022gei</bcf:citekey>
  </bcf:section>
  <!-- SECTION 0 (cont.) -->
  <bcf:section number="0">
  </bcf:section>
  <!-- SECTION 2 -->
  <bcf:bibdata section="2">
    <bcf:datasource type="file" datatype="bibtex" glob="false">myexample.bib</bcf:datasource>
  </bcf:bibdata>
  <bcf:section number="2">
    <bcf:citekey order="1">Pan:2021mrw</bcf:citekey>
    <bcf:citekey order="2">Pan:2021mrw</bcf:citekey>
    <bcf:citekey order="3">Zheng:2022zkm</bcf:citekey>
    <bcf:citekey order="4">Pan:2021ulr</bcf:citekey>
    <bcf:citekey order="5">Zheng:2022zkm</bcf:citekey>
    <bcf:citekey order="6">Pan:2023jjw</bcf:citekey>
    <bcf:citekey order="7">Pan:2023jjw</bcf:citekey>
    <bcf:citekey order="7">Pan:2025jjr</bcf:citekey>
    <bcf:citekey order="8">Pan:2021mrw</bcf:citekey>
    <bcf:citekey order="9">Zheng:2022zkm</bcf:citekey>
    <bcf:citekey order="10">Pan:2021ulr</bcf:citekey>
    <bcf:citekey order="11">Pan:2023jjw</bcf:citekey>
    <bcf:citekey order="12">Pan:2025jjr</bcf:citekey>
  </bcf:section>
  <!-- SECTION 0 (cont.) -->
  <bcf:section number="0">
  </bcf:section>
  <!-- SECTION 3 -->
  <bcf:bibdata section="3">
    <bcf:datasource type="file" datatype="bibtex" glob="false">myexample.bib</bcf:datasource>
  </bcf:bibdata>
  <bcf:section number="3">
    <bcf:citekey order="1">Pan:2024epf</bcf:citekey>
    <bcf:citekey order="1">Pan:2024hcz</bcf:citekey>
    <bcf:citekey order="1">Pan:2025gzh</bcf:citekey>
    <bcf:citekey order="2">Pan:2024epf</bcf:citekey>
    <bcf:citekey order="3">Pan:2024hcz</bcf:citekey>
    <bcf:citekey order="4">Pan:2025gzh</bcf:citekey>
  </bcf:section>
  <!-- SECTION 0 (cont.) -->
  <bcf:section number="0">
    <bcf:citekey order="1">Pan:2023jjw</bcf:citekey>
  </bcf:section>
  <!-- SECTION 4 -->
  <bcf:bibdata section="4">
    <bcf:datasource type="file" datatype="bibtex" glob="false">myexample.bib</bcf:datasource>
  </bcf:bibdata>
  <bcf:section number="4">
    <bcf:citekey order="1">Pan:2019bor</bcf:citekey>
    <bcf:citekey order="2">Wang:2020oxs</bcf:citekey>
    <bcf:citekey order="3">Pan:2020cgc</bcf:citekey>
    <bcf:citekey order="4">Pan:2019bor</bcf:citekey>
    <bcf:citekey order="5">Wang:2020oxs</bcf:citekey>
    <bcf:citekey order="6">Pan:2020cgc</bcf:citekey>
  </bcf:section>
  <!-- SECTION 0 (cont.) -->
  <bcf:section number="0">
  </bcf:section>
  <!-- SECTION 5 -->
  <bcf:bibdata section="5">
    <bcf:datasource type="file" datatype="bibtex" glob="false">myexample.bib</bcf:datasource>
  </bcf:bibdata>
  <bcf:section number="5">
    <bcf:citekey order="1">Nawata:2023aoq</bcf:citekey>
    <bcf:citekey order="2">Liu:2021fsw</bcf:citekey>
    <bcf:citekey order="2">Pan:2021mrw</bcf:citekey>
    <bcf:citekey order="3">Pan:2021mrw</bcf:citekey>
    <bcf:citekey order="3">Guo:2023mkn</bcf:citekey>
    <bcf:citekey order="4">Nawata:2023aoq</bcf:citekey>
    <bcf:citekey order="5">Liu:2021fsw</bcf:citekey>
    <bcf:citekey order="6">Pan:2021mrw</bcf:citekey>
    <bcf:citekey order="7">Guo:2023mkn</bcf:citekey>
  </bcf:section>
  <!-- SECTION 0 (cont.) -->
  <bcf:section number="0">
  </bcf:section>
  <!-- SECTION 6 -->
  <bcf:bibdata section="6">
    <bcf:datasource type="file" datatype="bibtex" glob="false">myexample.bib</bcf:datasource>
  </bcf:bibdata>
  <bcf:section number="6">
    <bcf:citekey order="1">Pan:2021ulr</bcf:citekey>
    <bcf:citekey order="2">Zheng:2022zkm</bcf:citekey>
    <bcf:citekey order="3">Pan:2023jjw</bcf:citekey>
    <bcf:citekey order="4">Pan:2021ulr</bcf:citekey>
    <bcf:citekey order="5">Zheng:2022zkm</bcf:citekey>
    <bcf:citekey order="6">Pan:2023jjw</bcf:citekey>
  </bcf:section>
  <!-- SECTION 0 (cont.) -->
  <bcf:section number="0">
    <bcf:citekey order="2">Pan:2023jjw</bcf:citekey>
  </bcf:section>
  <!-- SORTING TEMPLATES -->
  <bcf:sortingtemplate name="none">
    <bcf:sort order="1">
      <bcf:sortitem order="1">citeorder</bcf:sortitem>
    </bcf:sort>
  </bcf:sortingtemplate>
  <!-- DATALISTS -->
  <bcf:datalist section="1"
                name="none/global//global/global"
                type="entry"
                sortingtemplatename="none"
                sortingnamekeytemplatename="global"
                labelprefix=""
                uniquenametemplatename="global"
                labelalphanametemplatename="global">
  </bcf:datalist>
  <bcf:datalist section="2"
                name="none/global//global/global"
                type="entry"
                sortingtemplatename="none"
                sortingnamekeytemplatename="global"
                labelprefix=""
                uniquenametemplatename="global"
                labelalphanametemplatename="global">
  </bcf:datalist>
  <bcf:datalist section="3"
                name="none/global//global/global"
                type="entry"
                sortingtemplatename="none"
                sortingnamekeytemplatename="global"
                labelprefix=""
                uniquenametemplatename="global"
                labelalphanametemplatename="global">
  </bcf:datalist>
  <bcf:datalist section="4"
                name="none/global//global/global"
                type="entry"
                sortingtemplatename="none"
                sortingnamekeytemplatename="global"
                labelprefix=""
                uniquenametemplatename="global"
                labelalphanametemplatename="global">
  </bcf:datalist>
  <bcf:datalist section="5"
                name="none/global//global/global"
                type="entry"
                sortingtemplatename="none"
                sortingnamekeytemplatename="global"
                labelprefix=""
                uniquenametemplatename="global"
                labelalphanametemplatename="global">
  </bcf:datalist>
  <bcf:datalist section="6"
                name="none/global//global/global"
                type="entry"
                sortingtemplatename="none"
                sortingnamekeytemplatename="global"
                labelprefix=""
                uniquenametemplatename="global"
                labelalphanametemplatename="global">
  </bcf:datalist>
</bcf:controlfile>
